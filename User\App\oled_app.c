#include "oled_app.h"


void Oled_Init(void)
{
  OLED_Init();
  OLED_Clear();
}

void Oled_Task(void)
{
  extern bool pid_running;
  extern unsigned char point_count;
  extern unsigned char system_mode;
  extern unsigned int distance;
  extern float g_line_position_error;

  // 显示系统状态
  oled_printf(0, 0, "Square Track Mode");
  oled_printf(0, 1, "PID:%s Point:%d", pid_running ? "ON" : "OFF", point_count);
  oled_printf(0, 2, "Mode:%d Dist:%d", system_mode, distance);
  oled_printf(0, 3, "Error:%.2f", g_line_position_error);

  // 显示灰度传感器状态
  extern unsigned char Digtal;
  oled_printf(0, 4, "%d%d%d%d%d%d%d%d",
    (Digtal>>7)&0x01,(Digtal>>6)&0x01,(Digtal>>5)&0x01,(Digtal>>4)&0x01,
    (Digtal>>3)&0x01,(Digtal>>2)&0x01,(Digtal>>1)&0x01,(Digtal>>0)&0x01);

}
