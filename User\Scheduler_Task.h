#ifndef __SCHEDULER_TASK_H__
#define __SCHEDULER_TASK_H__

#include "MyDefine.h"

void System_Init(void);
void Car_State_Update(void);

// 状态机相关变量
extern unsigned char point_count;
extern unsigned char system_mode;
extern unsigned int distance;

// 路径点检测相关变量
extern unsigned char output_ff_flag;
extern unsigned int intput_timer500ms;
extern unsigned char intput_ff_flag;
extern unsigned int output_timer500ms;

// 定时器变量
extern unsigned char measure_timer5ms;
extern unsigned char key_timer10ms;

#endif
