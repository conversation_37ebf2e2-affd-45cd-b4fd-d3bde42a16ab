#include "btn_app.h"
#include "ebtn.h" // ??????
#include "gpio.h" // GPIO?????

extern uint8_t ucLed[4]; // ??LED????

typedef enum // ??????????
{
    USER_BUTTON_0 = 0, // ????0
    USER_BUTTON_1,     // ????1(????????/??)
    USER_BUTTON_2,     // ????2(5s????)
    USER_BUTTON_3,     // ????3(10s????)
    USER_BUTTON_4,     // ????4(15s????)
    USER_BUTTON_5,     // ????5
    USER_BUTTON_MAX,   // ????????

    //    USER_BUTTON_COMBO_0 = 0x100, // ??????(???)
    //    USER_BUTTON_COMBO_1,
    //    USER_BUTTON_COMBO_2,
    //    USER_BUTTON_COMBO_3,
    //    USER_BUTTON_COMBO_MAX,
} user_button_t;

// ????????????????????20ms?????????0ms????��??????20ms?????????1000ms????????????????????1000ms?????????10??
static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(20, 0, 20, 1000, 0, 1000, 10);

static ebtn_btn_t btns[] = { // ????????????
    EBTN_BUTTON_INIT(USER_BUTTON_0, &defaul_ebtn_param), // ????0
    EBTN_BUTTON_INIT(USER_BUTTON_1, &defaul_ebtn_param), // ????1
    EBTN_BUTTON_INIT(USER_BUTTON_2, &defaul_ebtn_param), // ????2
    EBTN_BUTTON_INIT(USER_BUTTON_3, &defaul_ebtn_param), // ????3
    EBTN_BUTTON_INIT(USER_BUTTON_4, &defaul_ebtn_param), // ????4
    EBTN_BUTTON_INIT(USER_BUTTON_5, &defaul_ebtn_param), // ????5
};

// static ebtn_btn_combo_t btns_combo[] = {
//     EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_0, &defaul_ebtn_param, EBTN_EVT_MASK_ONCLICK),
//     EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_1, &defaul_ebtn_param, EBTN_EVT_MASK_ONCLICK),
// };

uint8_t prv_btn_get_state(struct ebtn_btn *btn) 
{
    switch (btn->key_id) 
    {
    case USER_BUTTON_1:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_0); // ????0(PE15)????????��
    case USER_BUTTON_2:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_1); // ????1(PE13)????????��
    case USER_BUTTON_3:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_2); // ????2(PE11)????????��
    case USER_BUTTON_4:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_3);  // ????3(PE9)????????��
    default:
        return 0; 
    }
}

void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt) 
{
    if (evt == EBTN_EVT_ONPRESS) 
    {
        switch (btn->key_id) 
        {
        case USER_BUTTON_1:
          ucLed[0] ^= 1;
            break;
        case USER_BUTTON_2:
          ucLed[1] ^= 1;
            break;
        case USER_BUTTON_3:
          // 启动/停止PID控制
          extern bool pid_running;
          pid_running = !pid_running;
          ucLed[2] = pid_running ? 1 : 0;  // LED指示PID状态
            break;
        case USER_BUTTON_4:
          // 重置系统状态
          extern unsigned char point_count;
          extern unsigned int distance;
          point_count = 0;
          distance = 0;
          ucLed[3] ^= 1;  // LED闪烁表示重置
				    break;

        default:
            break;
        }
    }
}

void app_btn_init(void)
{
    // ebtn_init(btns, EBTN_ARRAY_SIZE(btns), btns_combo, EBTN_ARRAY_SIZE(btns_combo), prv_btn_get_state, prv_btn_event);
    ebtn_init(btns, EBTN_ARRAY_SIZE(btns), NULL, 0, prv_btn_get_state, prv_btn_event);

    //    ebtn_combo_btn_add_btn(&btns_combo[0], USER_BUTTON_0);
    //    ebtn_combo_btn_add_btn(&btns_combo[0], USER_BUTTON_1);

    //    ebtn_combo_btn_add_btn(&btns_combo[1], USER_BUTTON_2);
    //    ebtn_combo_btn_add_btn(&btns_combo[1], USER_BUTTON_3);

 
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
}

void btn_task(void)
{
    ebtn_process(uwTick);
}
