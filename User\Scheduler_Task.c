#include "Scheduler_Task.h"

extern Motor_t right_motor;
extern Motor_t left_motor; 

void System_Init(void)
{
  Led_Init();
  Motor_Init();	
  app_btn_init();
  Oled_Init();
  uart_init();
  Gray_Init();
  Encoder_Init();
  jy901s_init();
  PID_Init();
  my_printf(&huart1, "=== System Init ===\r\n");
  HAL_TIM_Base_Start_IT(&htim2);
}

extern Encoder left_encoder;
extern Encoder right_encoder;

extern Motor_t right_motor;
extern Motor_t left_motor; 

unsigned char measure_timer5ms;
unsigned char key_timer10ms;

unsigned char output_ff_flag;
unsigned int intput_timer500ms;

unsigned char intput_ff_flag;
unsigned int output_timer500ms;

unsigned int led_timer500ms; // ÿ����һ���㣬LED ���� 500ms ��Ϩ��

unsigned char point_count = 0; // �����ĵ�λ��������Ȧ + 1����Ȧ + 1��

unsigned char system_mode = 2; // ϵͳ״̬��1 ~ 4 ��Ӧ 4 ����Ŀ�� - ����Ϊģʽ2������һȦ

unsigned char circle_count = 0; // �������Ȧ��������

unsigned int distance = 0; // ��¼С��ÿһ����ʻ�ľ���

// TIM2 �жϷ�������1ms �жϣ�
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  if(htim->Instance != htim2.Instance) return;

  /* 10ms �����������*/
  if(++key_timer10ms >= 10)
  {
    key_timer10ms = 0;
//    btn_task();
  }

  /* 5ms ��������*/
  if(++measure_timer5ms >= 5)
  {
    measure_timer5ms = 0;
    // 这些任务现在由调度器处理，注释掉避免重复执行
    // Encoder_Task();
    extern Encoder left_encoder;
    distance += (unsigned int)(left_encoder.speed_cm_s * 0.1); // 累计距离，转换为整数
    // jy901s_task();
    // Gray_Task();
    // PID_Task();
  }

  /* 检测路径点 - 基于灰度传感器全黑或全白状态 */
  extern unsigned char Digtal;

  // 检测到全黑线（可能是路径点标记）
  if(Digtal == 0xFF)  // 全部传感器检测到黑线
  {
    output_ff_flag = 1;
    if(++intput_timer500ms >= 500) intput_timer500ms = 500;  // 减少延时，提高响应
  }
  else if(output_ff_flag == 1 && intput_timer500ms == 500)
  {
    output_ff_flag = 0;
    intput_timer500ms = 0;
    point_count++;
    Car_State_Update();
  }

  /* 备用检测方案：基于距离检测路径点 */
  if(distance > 9500)  // 接近100cm时检测路径点
  {
    intput_ff_flag = 1;
    if(++output_timer500ms >= 100) output_timer500ms = 100;
  }
  else if(intput_ff_flag == 1 && output_timer500ms == 100)
  {
    intput_ff_flag = 0;
    output_timer500ms = 0;
    point_count++;
    Car_State_Update();
  }

}

// ÿ�ε�λ��������ʱ������ϵͳ״̬ͬ������С����״̬
void Car_State_Update(void)
{
  // led_state = 1;  // 如果有LED状态变量，可以启用
  distance = 0;

  switch(system_mode)
  {
    case 1: // ��һ�⣺ֱ����ʻ A -> B
      if(point_count == 1)
      {
        pid_running = 0;
        motor_break();
      }
      break;
    case 2: // �ڶ��⣺����һȦ A -> B -> C -> D (正方形逆时针)
      if(point_count == 1)  // 到达B点
        pid_control_mode = 1; // ʹ��ѭ��������
      else if(point_count == 2)  // 到达C点
      {
        pid_control_mode = 0; // ʹ�ýǶȻ����� - 准备转弯
        // 设置转弯角度为-90度（逆时针转弯）
        extern PID_T pid_angle;
        pid_set_target(&pid_angle, -90);
      }
      else if(point_count == 3)  // 到达D点
        pid_control_mode = 1; // ʹ��ѭ��������
      else if(point_count == 4)  // 回到A点，完成一圈
      {
        point_count = 0;  // 重置计数，继续循环
        pid_control_mode = 1; // 继续循迹模式
        // 可以选择停止或继续循环
        // pid_running = 0;
        // motor_break();
      }
      break;
    case 3: // �����⣺8 �ֻ���һȦ A -> C -> B -> D
      if(point_count == 1)
      {
        pid_control_mode = 1; // ʹ��ѭ��������
      }
      else if(point_count == 2)
      {
        pid_control_mode = 0; // ʹ�ýǶȻ�����
        extern PID_T pid_angle;
        pid_set_target(&pid_angle, 253);
      }
      else if(point_count == 3)
      {
        pid_control_mode = 1; // ʹ��ѭ��������
      }
      else if(point_count == 4)
      {
        pid_running = 0;
        motor_break();
      }
      break;
    case 4: // �����⣺8 �ֻ�����Ȧ
      if(point_count == 1)
      {
        pid_control_mode = 1; // ʹ��ѭ��������
      }
      else if(point_count == 2)
      {
        pid_control_mode = 0; // ʹ�ýǶȻ�����
        extern PID_T pid_angle;
        pid_set_target(&pid_angle, 253 - (0.3 * circle_count)); // ����Ȧ������Ʈ�Ʋ���
      }
      else if(point_count == 3)
        pid_control_mode = 1; // ʹ��ѭ��������
      else if(point_count == 4)
      {
        if(++circle_count >= 4)
        {
          pid_running = 0;
          motor_break();
        }
        point_count = 0;
        pid_control_mode = 0; // ʹ�ýǶȻ�����
        extern PID_T pid_angle;
        pid_set_target(&pid_angle, 0 - (0.2 * circle_count)); // ����Ȧ������Ʈ�Ʋ���
      }
      break;
  }

  /* ������ʷ��� */
  extern PID_T pid_line, pid_angle;
  pid_reset(&pid_line);
  pid_reset(&pid_angle);
}
