--cpu=Cortex-M4.fp.sp
"car\startup_stm32f407xx.o"
"car\main.o"
"car\gpio.o"
"car\dma.o"
"car\i2c.o"
"car\tim.o"
"car\usart.o"
"car\stm32f4xx_it.o"
"car\stm32f4xx_hal_msp.o"
"car\stm32f4xx_hal_i2c.o"
"car\stm32f4xx_hal_i2c_ex.o"
"car\stm32f4xx_hal_rcc.o"
"car\stm32f4xx_hal_rcc_ex.o"
"car\stm32f4xx_hal_flash.o"
"car\stm32f4xx_hal_flash_ex.o"
"car\stm32f4xx_hal_flash_ramfunc.o"
"car\stm32f4xx_hal_gpio.o"
"car\stm32f4xx_hal_dma_ex.o"
"car\stm32f4xx_hal_dma.o"
"car\stm32f4xx_hal_pwr.o"
"car\stm32f4xx_hal_pwr_ex.o"
"car\stm32f4xx_hal_cortex.o"
"car\stm32f4xx_hal.o"
"car\stm32f4xx_hal_exti.o"
"car\stm32f4xx_hal_tim.o"
"car\stm32f4xx_hal_tim_ex.o"
"car\stm32f4xx_hal_uart.o"
"car\system_stm32f4xx.o"
"car\ringbuffer.o"
"car\hardware_iic.o"
"car\ebtn.o"
"car\oled.o"
"car\pid.o"
"car\oled_driver.o"
"car\led_driver.o"
"car\motor_driver.o"
"car\encoder_driver.o"
"car\jy901s_driver.o"
"car\oled_app.o"
"car\led_app.o"
"car\btn_app.o"
"car\uart_app.o"
"car\motor_app.o"
"car\encoder_app.o"
"car\gray_app.o"
"car\pid_app.o"
"car\jy901s_app.o"
"car\scheduler.o"
"car\scheduler_task.o"
--strict --scatter "Car\Car.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "Car.map" -o Car\Car.axf