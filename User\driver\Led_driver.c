#include "led_driver.h"
#include "gpio.h"

void Led_Display(uint8_t *ucLed)
{

    uint8_t temp = 0x00;

    static uint8_t temp_old = 0xff;

    for (int i = 0; i < 4; i++)
    {

        if (ucLed[i])
            temp |= (1 << i);
    }

    if (temp_old != temp)
    {

        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_11, (temp & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET);  // LED 1
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_12, (temp & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET);  // LED 2
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_9, (temp & 0x04) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 3
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_8, (temp & 0x08) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 4

        temp_old = temp;
    }
}