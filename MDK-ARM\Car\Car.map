Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA1_Stream0_IRQHandler) for DMA1_Stream0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.I2C1_EV_IRQHandler) for I2C1_EV_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.I2C2_EV_IRQHandler) for I2C2_EV_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.UART5_IRQHandler) for UART5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(.text.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to main.o(.text.SystemClock_Config) for SystemClock_Config
    main.o(.text.main) refers to gpio.o(.text.MX_GPIO_Init) for MX_GPIO_Init
    main.o(.text.main) refers to dma.o(.text.MX_DMA_Init) for MX_DMA_Init
    main.o(.text.main) refers to i2c.o(.text.MX_I2C2_Init) for MX_I2C2_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM6_Init) for MX_TIM6_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM1_Init) for MX_TIM1_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM3_Init) for MX_TIM3_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM4_Init) for MX_TIM4_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM2_Init) for MX_TIM2_Init
    main.o(.text.main) refers to i2c.o(.text.MX_I2C1_Init) for MX_I2C1_Init
    main.o(.text.main) refers to usart.o(.text.MX_UART4_Init) for MX_UART4_Init
    main.o(.text.main) refers to usart.o(.text.MX_UART5_Init) for MX_UART5_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(.text.main) refers to scheduler.o(.text.Scheduler_Init) for Scheduler_Init
    main.o(.text.main) refers to scheduler.o(.text.Scheduler_Run) for Scheduler_Run
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(.ARM.exidx.text.MX_GPIO_Init) refers to gpio.o(.text.MX_GPIO_Init) for [Anonymous Symbol]
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    dma.o(.ARM.exidx.text.MX_DMA_Init) refers to dma.o(.text.MX_DMA_Init) for [Anonymous Symbol]
    i2c.o(.text.MX_I2C1_Init) refers to i2c.o(.bss.hi2c1) for hi2c1
    i2c.o(.text.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(.text.MX_I2C1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    i2c.o(.ARM.exidx.text.MX_I2C1_Init) refers to i2c.o(.text.MX_I2C1_Init) for [Anonymous Symbol]
    i2c.o(.text.MX_I2C2_Init) refers to i2c.o(.bss.hi2c2) for hi2c2
    i2c.o(.text.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(.text.MX_I2C2_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    i2c.o(.ARM.exidx.text.MX_I2C2_Init) refers to i2c.o(.text.MX_I2C2_Init) for [Anonymous Symbol]
    i2c.o(.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    i2c.o(.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    i2c.o(.text.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(.text.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM1_Init) refers to tim.o(.bss.htim1) for htim1
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.MX_TIM1_Init) refers to tim.o(.text.MX_TIM1_Init) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.HAL_TIM_MspPostInit) refers to tim.o(.text.HAL_TIM_MspPostInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM2_Init) refers to tim.o(.bss.htim2) for htim2
    tim.o(.text.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM2_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(.text.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM2_Init) refers to tim.o(.text.MX_TIM2_Init) for [Anonymous Symbol]
    tim.o(.text.MX_TIM3_Init) refers to tim.o(.bss.htim3) for htim3
    tim.o(.text.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(.text.MX_TIM3_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM3_Init) refers to tim.o(.text.MX_TIM3_Init) for [Anonymous Symbol]
    tim.o(.text.MX_TIM4_Init) refers to tim.o(.bss.htim4) for htim4
    tim.o(.text.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(.text.MX_TIM4_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM4_Init) refers to tim.o(.text.MX_TIM4_Init) for [Anonymous Symbol]
    tim.o(.text.MX_TIM6_Init) refers to tim.o(.bss.htim6) for htim6
    tim.o(.text.MX_TIM6_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM6_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM6_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM6_Init) refers to tim.o(.text.MX_TIM6_Init) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(.text.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.MX_UART4_Init) refers to usart.o(.bss.huart4) for huart4
    usart.o(.text.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_UART4_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_UART4_Init) refers to usart.o(.text.MX_UART4_Init) for [Anonymous Symbol]
    usart.o(.text.MX_UART5_Init) refers to usart.o(.bss.huart5) for huart5
    usart.o(.text.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_UART5_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_UART5_Init) refers to usart.o(.text.MX_UART5_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.huart1) for huart1
    usart.o(.text.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART1_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART1_UART_Init) refers to usart.o(.text.MX_USART1_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART2_UART_Init) refers to usart.o(.bss.huart2) for huart2
    usart.o(.text.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART2_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART2_UART_Init) refers to usart.o(.text.MX_USART2_UART_Init) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(.text.HAL_UART_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_uart5_rx) for hdma_uart5_rx
    usart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to usart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f4xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f4xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f4xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f4xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f4xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f4xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.SysTick_Handler) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f4xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA1_Stream0_IRQHandler) refers to usart.o(.bss.hdma_uart5_rx) for hdma_uart5_rx
    stm32f4xx_it.o(.text.DMA1_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream0_IRQHandler) refers to stm32f4xx_it.o(.text.DMA1_Stream0_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.TIM2_IRQHandler) refers to tim.o(.bss.htim2) for htim2
    stm32f4xx_it.o(.text.TIM2_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.TIM2_IRQHandler) refers to stm32f4xx_it.o(.text.TIM2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.I2C1_EV_IRQHandler) refers to i2c.o(.bss.hi2c1) for hi2c1
    stm32f4xx_it.o(.text.I2C1_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) for HAL_I2C_EV_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.I2C1_EV_IRQHandler) refers to stm32f4xx_it.o(.text.I2C1_EV_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.I2C2_EV_IRQHandler) refers to i2c.o(.bss.hi2c2) for hi2c2
    stm32f4xx_it.o(.text.I2C2_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) for HAL_I2C_EV_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.I2C2_EV_IRQHandler) refers to stm32f4xx_it.o(.text.I2C2_EV_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to usart.o(.bss.huart1) for huart1
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.UART5_IRQHandler) refers to usart.o(.bss.huart5) for huart5
    stm32f4xx_it.o(.text.UART5_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.UART5_IRQHandler) refers to stm32f4xx_it.o(.text.UART5_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream2_IRQHandler) refers to stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to i2c.o(.text.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) refers to i2c.o(.text.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent) refers to stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f4xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f4xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f4xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f4xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f4xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_EnableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_DisableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to tim.o(.text.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) refers to btn_app.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAError) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to tim.o(.text.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) refers to tim.o(.text.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to btn_app.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GetChannelState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurstState) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to uart_app.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to uart_app.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to jy901s_app.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to jy901s_app.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to uart_app.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to uart_app.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for [Anonymous Symbol]
    system_stm32f4xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f4xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_status) refers to ringbuffer.o(.text.rt_ringbuffer_status) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_init) refers to ringbuffer.o(.text.rt_ringbuffer_init) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_put) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put) refers to ringbuffer.o(.text.rt_ringbuffer_put) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_data_len) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_put_force) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put_force) refers to ringbuffer.o(.text.rt_ringbuffer_put_force) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_get) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_get) refers to ringbuffer.o(.text.rt_ringbuffer_get) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_peek) refers to ringbuffer.o(.text.rt_ringbuffer_peek) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar) refers to ringbuffer.o(.text.rt_ringbuffer_putchar) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar_force) refers to ringbuffer.o(.text.rt_ringbuffer_putchar_force) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_getchar) refers to ringbuffer.o(.text.rt_ringbuffer_getchar) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_reset) refers to ringbuffer.o(.text.rt_ringbuffer_reset) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_ReadByte) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_ReadByte) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    hardware_iic.o(.ARM.exidx.text.IIC_ReadByte) refers to hardware_iic.o(.text.IIC_ReadByte) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_ReadBytes) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_ReadBytes) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_ReadBytes) refers to hardware_iic.o(.text.IIC_ReadBytes) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_WriteByte) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_WriteByte) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    hardware_iic.o(.ARM.exidx.text.IIC_WriteByte) refers to hardware_iic.o(.text.IIC_WriteByte) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_WriteBytes) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_WriteBytes) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    hardware_iic.o(.ARM.exidx.text.IIC_WriteBytes) refers to hardware_iic.o(.text.IIC_WriteBytes) for [Anonymous Symbol]
    hardware_iic.o(.text.Ping) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.Ping) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.Ping) refers to hardware_iic.o(.text.Ping) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Digtal) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Get_Digtal) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Digtal) refers to hardware_iic.o(.text.IIC_Get_Digtal) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Anolog) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Get_Anolog) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Anolog) refers to hardware_iic.o(.text.IIC_Get_Anolog) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Single_Anolog) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Get_Single_Anolog) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Single_Anolog) refers to hardware_iic.o(.text.IIC_Get_Single_Anolog) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Anolog_Normalize) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Anolog_Normalize) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    hardware_iic.o(.ARM.exidx.text.IIC_Anolog_Normalize) refers to hardware_iic.o(.text.IIC_Anolog_Normalize) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Offset) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Get_Offset) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Offset) refers to hardware_iic.o(.text.IIC_Get_Offset) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_init) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.text.ebtn_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ebtn.o(.ARM.exidx.text.ebtn_init) refers to ebtn.o(.text.ebtn_init) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_process_with_curr_state) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.text.ebtn_process_with_curr_state) refers to memcmp.o(.text) for memcmp
    ebtn.o(.text.ebtn_process_with_curr_state) refers to ebtn.o(.text.prv_process_btn) for prv_process_btn
    ebtn.o(.ARM.exidx.text.ebtn_process_with_curr_state) refers to ebtn.o(.text.ebtn_process_with_curr_state) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_process) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.text.ebtn_process) refers to ebtn.o(.text.ebtn_process_with_curr_state) for ebtn_process_with_curr_state
    ebtn.o(.ARM.exidx.text.ebtn_process) refers to ebtn.o(.text.ebtn_process) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_get_total_btn_cnt) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.ARM.exidx.text.ebtn_get_total_btn_cnt) refers to ebtn.o(.text.ebtn_get_total_btn_cnt) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_get_btn_index_by_key_id) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.ARM.exidx.text.ebtn_get_btn_index_by_key_id) refers to ebtn.o(.text.ebtn_get_btn_index_by_key_id) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_get_btn_by_key_id) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.ARM.exidx.text.ebtn_get_btn_by_key_id) refers to ebtn.o(.text.ebtn_get_btn_by_key_id) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_get_btn_index_by_btn) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.ARM.exidx.text.ebtn_get_btn_index_by_btn) refers to ebtn.o(.text.ebtn_get_btn_index_by_btn) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_get_btn_index_by_btn_dyn) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.ARM.exidx.text.ebtn_get_btn_index_by_btn_dyn) refers to ebtn.o(.text.ebtn_get_btn_index_by_btn_dyn) for [Anonymous Symbol]
    ebtn.o(.ARM.exidx.text.ebtn_combo_btn_add_btn_by_idx) refers to ebtn.o(.text.ebtn_combo_btn_add_btn_by_idx) for [Anonymous Symbol]
    ebtn.o(.ARM.exidx.text.ebtn_combo_btn_remove_btn_by_idx) refers to ebtn.o(.text.ebtn_combo_btn_remove_btn_by_idx) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_combo_btn_add_btn) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.ARM.exidx.text.ebtn_combo_btn_add_btn) refers to ebtn.o(.text.ebtn_combo_btn_add_btn) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_combo_btn_remove_btn) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.ARM.exidx.text.ebtn_combo_btn_remove_btn) refers to ebtn.o(.text.ebtn_combo_btn_remove_btn) for [Anonymous Symbol]
    ebtn.o(.ARM.exidx.text.ebtn_is_btn_active) refers to ebtn.o(.text.ebtn_is_btn_active) for [Anonymous Symbol]
    ebtn.o(.ARM.exidx.text.ebtn_is_btn_in_process) refers to ebtn.o(.text.ebtn_is_btn_in_process) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_is_in_process) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.ARM.exidx.text.ebtn_is_in_process) refers to ebtn.o(.text.ebtn_is_in_process) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_register) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.ARM.exidx.text.ebtn_register) refers to ebtn.o(.text.ebtn_register) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_combo_register) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.ARM.exidx.text.ebtn_combo_register) refers to ebtn.o(.text.ebtn_combo_register) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_set_config) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.ARM.exidx.text.ebtn_set_config) refers to ebtn.o(.text.ebtn_set_config) for [Anonymous Symbol]
    ebtn.o(.text.ebtn_get_config) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.ARM.exidx.text.ebtn_get_config) refers to ebtn.o(.text.ebtn_get_config) for [Anonymous Symbol]
    ebtn.o(.text.prv_process_btn) refers to ebtn.o(.bss.ebtn_default) for ebtn_default
    ebtn.o(.ARM.exidx.text.prv_process_btn) refers to ebtn.o(.text.prv_process_btn) for [Anonymous Symbol]
    oled.o(.text.OLED_Write_cmd) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Write_cmd) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_Write_cmd) refers to oled.o(.text.OLED_Write_cmd) for [Anonymous Symbol]
    oled.o(.text.OLED_Write_data) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Write_data) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_Write_data) refers to oled.o(.text.OLED_Write_data) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowPic) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_ShowPic) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_ShowPic) refers to oled.o(.text.OLED_ShowPic) for [Anonymous Symbol]
    oled.o(.text.OLED_Set_Position) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Set_Position) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_Set_Position) refers to oled.o(.text.OLED_Set_Position) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowHanzi) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_ShowHanzi) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.text.OLED_ShowHanzi) refers to oled.o(.rodata.Hzk) for Hzk
    oled.o(.ARM.exidx.text.OLED_ShowHanzi) refers to oled.o(.text.OLED_ShowHanzi) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowHzbig) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_ShowHzbig) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.text.OLED_ShowHzbig) refers to oled.o(.rodata.Hzb) for Hzb
    oled.o(.ARM.exidx.text.OLED_ShowHzbig) refers to oled.o(.text.OLED_ShowHzbig) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowFloat) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowFloat) refers to oled.o(.text.OLED_ShowFloat) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChar) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_ShowChar) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.F8X16) for F8X16
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.F6X8) for F6X8
    oled.o(.ARM.exidx.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowNum) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowNum) refers to oled.o(.text.OLED_ShowNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowStr) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowStr) refers to oled.o(.text.OLED_ShowStr) for [Anonymous Symbol]
    oled.o(.text.OLED_Allfill) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Allfill) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_Allfill) refers to oled.o(.text.OLED_Allfill) for [Anonymous Symbol]
    oled.o(.text.OLED_Clear) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Clear) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_Clear) refers to oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled.o(.text.OLED_Display_On) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Display_On) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_Display_On) refers to oled.o(.text.OLED_Display_On) for [Anonymous Symbol]
    oled.o(.text.OLED_Display_Off) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Display_Off) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_Display_Off) refers to oled.o(.text.OLED_Display_Off) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    oled.o(.text.OLED_Init) refers to oled.o(.data.initcmd1) for initcmd1
    oled.o(.text.OLED_Init) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_init) refers to pid.o(.text.pid_init) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_target) refers to pid.o(.text.pid_set_target) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_params) refers to pid.o(.text.pid_set_params) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_limit) refers to pid.o(.text.pid_set_limit) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_reset) refers to pid.o(.text.pid_reset) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_calculate_positional) refers to pid.o(.text.pid_calculate_positional) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_calculate_incremental) refers to pid.o(.text.pid_calculate_incremental) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_constrain) refers to pid.o(.text.pid_constrain) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_app_limit_integral) refers to pid.o(.text.pid_app_limit_integral) for [Anonymous Symbol]
    oled_driver.o(.text.oled_printf) refers to vsnprintf.o(.text) for vsnprintf
    oled_driver.o(.text.oled_printf) refers to oled.o(.text.OLED_ShowStr) for OLED_ShowStr
    oled_driver.o(.ARM.exidx.text.oled_printf) refers to oled_driver.o(.text.oled_printf) for [Anonymous Symbol]
    led_driver.o(.text.Led_Display) refers to led_driver.o(.data.Led_Display.temp_old) for Led_Display.temp_old
    led_driver.o(.text.Led_Display) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led_driver.o(.ARM.exidx.text.Led_Display) refers to led_driver.o(.text.Led_Display) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Create) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    motor_driver.o(.text.Motor_Create) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    motor_driver.o(.text.Motor_Create) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(.ARM.exidx.text.Motor_Create) refers to motor_driver.o(.text.Motor_Create) for [Anonymous Symbol]
    motor_driver.o(.text.DRV8871_Control) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    motor_driver.o(.text.DRV8871_Control) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(.ARM.exidx.text.DRV8871_Control) refers to motor_driver.o(.text.DRV8871_Control) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_SetSpeed) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    motor_driver.o(.text.Motor_SetSpeed) refers to motor_driver.o(.text.DRV8871_Control) for DRV8871_Control
    motor_driver.o(.ARM.exidx.text.Motor_SetSpeed) refers to motor_driver.o(.text.Motor_SetSpeed) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Stop) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    motor_driver.o(.text.Motor_Stop) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(.ARM.exidx.text.Motor_Stop) refers to motor_driver.o(.text.Motor_Stop) for [Anonymous Symbol]
    motor_driver.o(.ARM.exidx.text.Motor_GetState) refers to motor_driver.o(.text.Motor_GetState) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Enable) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    motor_driver.o(.text.Motor_Enable) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(.ARM.exidx.text.Motor_Enable) refers to motor_driver.o(.text.Motor_Enable) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_SetDecayMode) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    motor_driver.o(.text.Motor_SetDecayMode) refers to motor_driver.o(.text.DRV8871_Control) for DRV8871_Control
    motor_driver.o(.ARM.exidx.text.Motor_SetDecayMode) refers to motor_driver.o(.text.Motor_SetDecayMode) for [Anonymous Symbol]
    encoder_driver.o(.text.Encoder_Driver_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    encoder_driver.o(.ARM.exidx.text.Encoder_Driver_Init) refers to encoder_driver.o(.text.Encoder_Driver_Init) for [Anonymous Symbol]
    encoder_driver.o(.text.Encoder_Driver_Update) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    encoder_driver.o(.text.Encoder_Driver_Update) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    encoder_driver.o(.text.Encoder_Driver_Update) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    encoder_driver.o(.ARM.exidx.text.Encoder_Driver_Update) refers to encoder_driver.o(.text.Encoder_Driver_Update) for [Anonymous Symbol]
    jy901s_driver.o(.text.jy901s_set) refers to jy901s_app.o(.bss.jy901s) for jy901s
    jy901s_driver.o(.text.jy901s_set) refers to usart.o(.bss.huart1) for huart1
    jy901s_driver.o(.text.jy901s_set) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.jy901s_set) refers to jy901s_driver.o(.text.JY901S_SetBaudRate) for JY901S_SetBaudRate
    jy901s_driver.o(.text.jy901s_set) refers to jy901s_driver.o(.rodata.str1.1) for .L.str
    jy901s_driver.o(.text.jy901s_set) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.text.jy901s_set) refers to jy901s_driver.o(.rodata..L.str.35) for .L.str.35
    jy901s_driver.o(.text.jy901s_set) refers to jy901s_driver.o(.text.JY901S_SetContent) for JY901S_SetContent
    jy901s_driver.o(.text.jy901s_set) refers to jy901s_driver.o(.text.JY901S_SetBandwidth) for JY901S_SetBandwidth
    jy901s_driver.o(.text.jy901s_set) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    jy901s_driver.o(.ARM.exidx.text.jy901s_set) refers to jy901s_driver.o(.text.jy901s_set) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_SetBaudRate) refers to jy901s_driver.o(.rodata.str1.1) for .L.str.39
    jy901s_driver.o(.text.JY901S_SetBaudRate) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.JY901S_SetBaudRate) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.text.JY901S_SetBaudRate) refers to jy901s_driver.o(.rodata..L.str.35) for .L.str.35
    jy901s_driver.o(.ARM.exidx.text.JY901S_SetBaudRate) refers to jy901s_driver.o(.text.JY901S_SetBaudRate) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_SetOutputRate) refers to jy901s_driver.o(.rodata.str1.1) for .L.str.39
    jy901s_driver.o(.text.JY901S_SetOutputRate) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.JY901S_SetOutputRate) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.text.JY901S_SetOutputRate) refers to jy901s_driver.o(.text.JY901S_SaveConfig) for JY901S_SaveConfig
    jy901s_driver.o(.ARM.exidx.text.JY901S_SetOutputRate) refers to jy901s_driver.o(.text.JY901S_SetOutputRate) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_SetContent) refers to jy901s_driver.o(.rodata.str1.1) for .L.str.39
    jy901s_driver.o(.text.JY901S_SetContent) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.JY901S_SetContent) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.text.JY901S_SetContent) refers to jy901s_driver.o(.rodata..L.str.35) for .L.str.35
    jy901s_driver.o(.ARM.exidx.text.JY901S_SetContent) refers to jy901s_driver.o(.text.JY901S_SetContent) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_SetBandwidth) refers to jy901s_driver.o(.rodata.str1.1) for .L.str.39
    jy901s_driver.o(.text.JY901S_SetBandwidth) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.JY901S_SetBandwidth) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.text.JY901S_SetBandwidth) refers to jy901s_driver.o(.rodata..L.str.35) for .L.str.35
    jy901s_driver.o(.ARM.exidx.text.JY901S_SetBandwidth) refers to jy901s_driver.o(.text.JY901S_SetBandwidth) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_WatchdogInit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    jy901s_driver.o(.ARM.exidx.text.JY901S_WatchdogInit) refers to jy901s_driver.o(.text.JY901S_WatchdogInit) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_WatchdogEnable) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    jy901s_driver.o(.ARM.exidx.text.JY901S_WatchdogEnable) refers to jy901s_driver.o(.text.JY901S_WatchdogEnable) for [Anonymous Symbol]
    jy901s_driver.o(.text.jy901s_watchdog_init) refers to jy901s_app.o(.bss.jy901s) for jy901s
    jy901s_driver.o(.text.jy901s_watchdog_init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    jy901s_driver.o(.text.jy901s_watchdog_init) refers to usart.o(.bss.huart1) for huart1
    jy901s_driver.o(.text.jy901s_watchdog_init) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.jy901s_watchdog_init) refers to jy901s_driver.o(.rodata.str1.1) for .L.str
    jy901s_driver.o(.ARM.exidx.text.jy901s_watchdog_init) refers to jy901s_driver.o(.text.jy901s_watchdog_init) for [Anonymous Symbol]
    jy901s_driver.o(.text.jy901s_calibration) refers to jy901s_app.o(.bss.jy901s) for jy901s
    jy901s_driver.o(.text.jy901s_calibration) refers to usart.o(.bss.huart1) for huart1
    jy901s_driver.o(.text.jy901s_calibration) refers to jy901s_driver.o(.rodata.str1.1) for .L.str.17
    jy901s_driver.o(.text.jy901s_calibration) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.jy901s_calibration) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.text.jy901s_calibration) refers to jy901s_driver.o(.text.JY901S_StartAccCalibration) for JY901S_StartAccCalibration
    jy901s_driver.o(.text.jy901s_calibration) refers to jy901s_driver.o(.text.JY901S_StopAccCalibration) for JY901S_StopAccCalibration
    jy901s_driver.o(.text.jy901s_calibration) refers to jy901s_driver.o(.text.JY901S_StartMagCalibration) for JY901S_StartMagCalibration
    jy901s_driver.o(.text.jy901s_calibration) refers to jy901s_driver.o(.text.JY901S_StopMagCalibration) for JY901S_StopMagCalibration
    jy901s_driver.o(.ARM.exidx.text.jy901s_calibration) refers to jy901s_driver.o(.text.jy901s_calibration) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_StartAccCalibration) refers to jy901s_driver.o(.rodata.str1.1) for .L.str.39
    jy901s_driver.o(.text.JY901S_StartAccCalibration) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.JY901S_StartAccCalibration) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.text.JY901S_StartAccCalibration) refers to jy901s_driver.o(.rodata..L.str.35) for .L.str.35
    jy901s_driver.o(.ARM.exidx.text.JY901S_StartAccCalibration) refers to jy901s_driver.o(.text.JY901S_StartAccCalibration) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_StopAccCalibration) refers to jy901s_driver.o(.rodata.str1.1) for .L.str.39
    jy901s_driver.o(.text.JY901S_StopAccCalibration) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.JY901S_StopAccCalibration) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.text.JY901S_StopAccCalibration) refers to jy901s_driver.o(.rodata..L.str.35) for .L.str.35
    jy901s_driver.o(.ARM.exidx.text.JY901S_StopAccCalibration) refers to jy901s_driver.o(.text.JY901S_StopAccCalibration) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_StartMagCalibration) refers to jy901s_driver.o(.rodata.str1.1) for .L.str.39
    jy901s_driver.o(.text.JY901S_StartMagCalibration) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.JY901S_StartMagCalibration) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.text.JY901S_StartMagCalibration) refers to jy901s_driver.o(.rodata..L.str.35) for .L.str.35
    jy901s_driver.o(.ARM.exidx.text.JY901S_StartMagCalibration) refers to jy901s_driver.o(.text.JY901S_StartMagCalibration) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_StopMagCalibration) refers to jy901s_driver.o(.rodata.str1.1) for .L.str.39
    jy901s_driver.o(.text.JY901S_StopMagCalibration) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.JY901S_StopMagCalibration) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.text.JY901S_StopMagCalibration) refers to jy901s_driver.o(.rodata..L.str.35) for .L.str.35
    jy901s_driver.o(.ARM.exidx.text.JY901S_StopMagCalibration) refers to jy901s_driver.o(.text.JY901S_StopMagCalibration) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_Create) refers to rt_memclr.o(.text) for __aeabi_memclr
    jy901s_driver.o(.text.JY901S_Create) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    jy901s_driver.o(.ARM.exidx.text.JY901S_Create) refers to jy901s_driver.o(.text.JY901S_Create) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_ProcessBuffer) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    jy901s_driver.o(.ARM.exidx.text.JY901S_ProcessBuffer) refers to jy901s_driver.o(.text.JY901S_ProcessBuffer) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_WatchdogFeed) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    jy901s_driver.o(.ARM.exidx.text.JY901S_WatchdogFeed) refers to jy901s_driver.o(.text.JY901S_WatchdogFeed) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetState) refers to jy901s_driver.o(.text.JY901S_GetState) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_Enable) refers to jy901s_driver.o(.text.JY901S_Enable) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetGyroZ) refers to jy901s_driver.o(.text.JY901S_GetGyroZ) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetGyroX) refers to jy901s_driver.o(.text.JY901S_GetGyroX) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetGyroY) refers to jy901s_driver.o(.text.JY901S_GetGyroY) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetYaw) refers to jy901s_driver.o(.text.JY901S_GetYaw) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetRoll) refers to jy901s_driver.o(.text.JY901S_GetRoll) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetPitch) refers to jy901s_driver.o(.text.JY901S_GetPitch) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetData) refers to jy901s_driver.o(.text.JY901S_GetData) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetAccX) refers to jy901s_driver.o(.text.JY901S_GetAccX) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetAccY) refers to jy901s_driver.o(.text.JY901S_GetAccY) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetAccZ) refers to jy901s_driver.o(.text.JY901S_GetAccZ) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetTemperature) refers to jy901s_driver.o(.text.JY901S_GetTemperature) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetMagX) refers to jy901s_driver.o(.text.JY901S_GetMagX) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetMagY) refers to jy901s_driver.o(.text.JY901S_GetMagY) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetMagZ) refers to jy901s_driver.o(.text.JY901S_GetMagZ) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_SaveConfig) refers to jy901s_driver.o(.rodata..L.str.35) for .L.str.35
    jy901s_driver.o(.text.JY901S_SaveConfig) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.JY901S_SaveConfig) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.ARM.exidx.text.JY901S_SaveConfig) refers to jy901s_driver.o(.text.JY901S_SaveConfig) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_AutoScanBaudRate) refers to jy901s_driver.o(.rodata.str1.1) for .L.str.36
    jy901s_driver.o(.text.JY901S_AutoScanBaudRate) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    jy901s_driver.o(.text.JY901S_AutoScanBaudRate) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.JY901S_AutoScanBaudRate) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.ARM.exidx.text.JY901S_AutoScanBaudRate) refers to jy901s_driver.o(.text.JY901S_AutoScanBaudRate) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_ReadRegister) refers to jy901s_driver.o(.rodata.str1.1) for .L.str.36
    jy901s_driver.o(.text.JY901S_ReadRegister) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.ARM.exidx.text.JY901S_ReadRegister) refers to jy901s_driver.o(.text.JY901S_ReadRegister) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_StartManualCalibration) refers to jy901s_driver.o(.rodata.str1.1) for .L.str.39
    jy901s_driver.o(.text.JY901S_StartManualCalibration) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.JY901S_StartManualCalibration) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.text.JY901S_StartManualCalibration) refers to jy901s_driver.o(.rodata..L.str.35) for .L.str.35
    jy901s_driver.o(.ARM.exidx.text.JY901S_StartManualCalibration) refers to jy901s_driver.o(.text.JY901S_StartManualCalibration) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_StopManualCalibration) refers to jy901s_driver.o(.rodata.str1.1) for .L.str.39
    jy901s_driver.o(.text.JY901S_StopManualCalibration) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.JY901S_StopManualCalibration) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.text.JY901S_StopManualCalibration) refers to jy901s_driver.o(.rodata..L.str.35) for .L.str.35
    jy901s_driver.o(.ARM.exidx.text.JY901S_StopManualCalibration) refers to jy901s_driver.o(.text.JY901S_StopManualCalibration) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_ResetYaw) refers to jy901s_driver.o(.rodata.str1.1) for .L.str.39
    jy901s_driver.o(.text.JY901S_ResetYaw) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.JY901S_ResetYaw) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.text.JY901S_ResetYaw) refers to jy901s_driver.o(.rodata..L.str.35) for .L.str.35
    jy901s_driver.o(.ARM.exidx.text.JY901S_ResetYaw) refers to jy901s_driver.o(.text.JY901S_ResetYaw) for [Anonymous Symbol]
    jy901s_driver.o(.text.JY901S_WatchdogCheck) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    jy901s_driver.o(.text.JY901S_WatchdogCheck) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) for HAL_UART_AbortReceive
    jy901s_driver.o(.text.JY901S_WatchdogCheck) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) for HAL_UART_DeInit
    jy901s_driver.o(.text.JY901S_WatchdogCheck) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    jy901s_driver.o(.text.JY901S_WatchdogCheck) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    jy901s_driver.o(.text.JY901S_WatchdogCheck) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_driver.o(.text.JY901S_WatchdogCheck) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    jy901s_driver.o(.text.JY901S_WatchdogCheck) refers to rt_memclr.o(.text) for __aeabi_memclr
    jy901s_driver.o(.ARM.exidx.text.JY901S_WatchdogCheck) refers to jy901s_driver.o(.text.JY901S_WatchdogCheck) for [Anonymous Symbol]
    jy901s_driver.o(.ARM.exidx.text.JY901S_GetRestartCount) refers to jy901s_driver.o(.text.JY901S_GetRestartCount) for [Anonymous Symbol]
    oled_app.o(.text.Oled_Init) refers to oled.o(.text.OLED_Init) for OLED_Init
    oled_app.o(.text.Oled_Init) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    oled_app.o(.ARM.exidx.text.Oled_Init) refers to oled_app.o(.text.Oled_Init) for [Anonymous Symbol]
    oled_app.o(.text.Oled_Task) refers to oled_driver.o(.text.oled_printf) for oled_printf
    oled_app.o(.text.Oled_Task) refers to gray_app.o(.bss..L_MergedGlobals) for Digtal
    oled_app.o(.ARM.exidx.text.Oled_Task) refers to oled_app.o(.text.Oled_Task) for [Anonymous Symbol]
    led_app.o(.text.Led_Init) refers to led_driver.o(.text.Led_Display) for Led_Display
    led_app.o(.ARM.exidx.text.Led_Init) refers to led_app.o(.text.Led_Init) for [Anonymous Symbol]
    led_app.o(.text.Led_Task) refers to led_app.o(.data.ucLed) for ucLed
    led_app.o(.text.Led_Task) refers to led_driver.o(.text.Led_Display) for Led_Display
    led_app.o(.ARM.exidx.text.Led_Task) refers to led_app.o(.text.Led_Task) for [Anonymous Symbol]
    btn_app.o(.text.prv_btn_get_state) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    btn_app.o(.ARM.exidx.text.prv_btn_get_state) refers to btn_app.o(.text.prv_btn_get_state) for [Anonymous Symbol]
    btn_app.o(.text.prv_btn_event) refers to led_app.o(.data.ucLed) for ucLed
    btn_app.o(.ARM.exidx.text.prv_btn_event) refers to btn_app.o(.text.prv_btn_event) for [Anonymous Symbol]
    btn_app.o(.text.app_btn_init) refers to btn_app.o(.text.prv_btn_event) for prv_btn_event
    btn_app.o(.text.app_btn_init) refers to btn_app.o(.text.prv_btn_get_state) for prv_btn_get_state
    btn_app.o(.text.app_btn_init) refers to btn_app.o(.data.btns) for btns
    btn_app.o(.text.app_btn_init) refers to ebtn.o(.text.ebtn_init) for ebtn_init
    btn_app.o(.ARM.exidx.text.app_btn_init) refers to btn_app.o(.text.app_btn_init) for [Anonymous Symbol]
    btn_app.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback) refers to btn_app.o(.text.HAL_TIM_PeriodElapsedCallback) for [Anonymous Symbol]
    btn_app.o(.text.btn_task) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    btn_app.o(.text.btn_task) refers to ebtn.o(.text.ebtn_process) for ebtn_process
    btn_app.o(.ARM.exidx.text.btn_task) refers to btn_app.o(.text.btn_task) for [Anonymous Symbol]
    btn_app.o(.data.btns) refers to btn_app.o(.rodata.defaul_ebtn_param) for [Anonymous Symbol]
    uart_app.o(.text.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    uart_app.o(.text.my_printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    uart_app.o(.ARM.exidx.text.my_printf) refers to uart_app.o(.text.my_printf) for [Anonymous Symbol]
    uart_app.o(.text.uart_init) refers to uart_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    uart_app.o(.text.uart_init) refers to uart_app.o(.bss.uart_ringbuffer_pool) for uart_ringbuffer_pool
    uart_app.o(.text.uart_init) refers to ringbuffer.o(.text.rt_ringbuffer_init) for rt_ringbuffer_init
    uart_app.o(.text.uart_init) refers to usart.o(.bss.huart1) for huart1
    uart_app.o(.text.uart_init) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_app.o(.text.uart_init) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    uart_app.o(.ARM.exidx.text.uart_init) refers to uart_app.o(.text.uart_init) for [Anonymous Symbol]
    uart_app.o(.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_app.o(.text.HAL_UARTEx_RxEventCallback) refers to uart_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    uart_app.o(.text.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(.text.rt_ringbuffer_put) for rt_ringbuffer_put
    uart_app.o(.text.HAL_UARTEx_RxEventCallback) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    uart_app.o(.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss.huart1) for huart1
    uart_app.o(.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_app.o(.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    uart_app.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to uart_app.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    uart_app.o(.text.uart_task) refers to uart_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    uart_app.o(.text.uart_task) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_app.o(.text.uart_task) refers to uart_app.o(.bss.uart_out_buffer) for uart_out_buffer
    uart_app.o(.text.uart_task) refers to ringbuffer.o(.text.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_app.o(.text.uart_task) refers to usart.o(.bss.huart1) for huart1
    uart_app.o(.text.uart_task) refers to uart_app.o(.text.my_printf) for my_printf
    uart_app.o(.text.uart_task) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    uart_app.o(.ARM.exidx.text.uart_task) refers to uart_app.o(.text.uart_task) for [Anonymous Symbol]
    motor_app.o(.text.Motor_Init) refers to motor_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    motor_app.o(.text.Motor_Init) refers to tim.o(.bss.htim1) for htim1
    motor_app.o(.text.Motor_Init) refers to motor_driver.o(.text.Motor_Create) for Motor_Create
    motor_app.o(.ARM.exidx.text.Motor_Init) refers to motor_app.o(.text.Motor_Init) for [Anonymous Symbol]
    motor_app.o(.text.motor_set_l) refers to motor_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    motor_app.o(.text.motor_set_l) refers to motor_driver.o(.text.Motor_SetSpeed) for Motor_SetSpeed
    motor_app.o(.ARM.exidx.text.motor_set_l) refers to motor_app.o(.text.motor_set_l) for [Anonymous Symbol]
    motor_app.o(.text.motor_set_r) refers to motor_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    motor_app.o(.text.motor_set_r) refers to motor_driver.o(.text.Motor_SetSpeed) for Motor_SetSpeed
    motor_app.o(.ARM.exidx.text.motor_set_r) refers to motor_app.o(.text.motor_set_r) for [Anonymous Symbol]
    motor_app.o(.text.motor_break) refers to motor_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    motor_app.o(.text.motor_break) refers to motor_driver.o(.text.Motor_Stop) for Motor_Stop
    motor_app.o(.ARM.exidx.text.motor_break) refers to motor_app.o(.text.motor_break) for [Anonymous Symbol]
    encoder_app.o(.text.Encoder_Init) refers to encoder_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    encoder_app.o(.text.Encoder_Init) refers to tim.o(.bss.htim3) for htim3
    encoder_app.o(.text.Encoder_Init) refers to encoder_driver.o(.text.Encoder_Driver_Init) for Encoder_Driver_Init
    encoder_app.o(.text.Encoder_Init) refers to tim.o(.bss.htim4) for htim4
    encoder_app.o(.ARM.exidx.text.Encoder_Init) refers to encoder_app.o(.text.Encoder_Init) for [Anonymous Symbol]
    encoder_app.o(.text.Encoder_Task) refers to encoder_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    encoder_app.o(.text.Encoder_Task) refers to encoder_driver.o(.text.Encoder_Driver_Update) for Encoder_Driver_Update
    encoder_app.o(.text.Encoder_Task) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    encoder_app.o(.text.Encoder_Task) refers to usart.o(.bss.huart1) for huart1
    encoder_app.o(.text.Encoder_Task) refers to uart_app.o(.text.my_printf) for my_printf
    encoder_app.o(.ARM.exidx.text.Encoder_Task) refers to encoder_app.o(.text.Encoder_Task) for [Anonymous Symbol]
    gray_app.o(.ARM.exidx.text.Gray_Init) refers to gray_app.o(.text.Gray_Init) for [Anonymous Symbol]
    gray_app.o(.text.Gray_Task) refers to hardware_iic.o(.text.IIC_Get_Digtal) for IIC_Get_Digtal
    gray_app.o(.text.Gray_Task) refers to gray_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gray_app.o(.text.Gray_Task) refers to gray_app.o(.data.gray_weights) for gray_weights
    gray_app.o(.ARM.exidx.text.Gray_Task) refers to gray_app.o(.text.Gray_Task) for [Anonymous Symbol]
    pid_app.o(.text.PID_Init) refers to pid_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    pid_app.o(.text.PID_Init) refers to pid_app.o(.bss..L_MergedGlobals.1) for .L_MergedGlobals.1
    pid_app.o(.text.PID_Init) refers to pid.o(.text.pid_init) for pid_init
    pid_app.o(.text.PID_Init) refers to pid_app.o(.bss..L_MergedGlobals.2) for .L_MergedGlobals.2
    pid_app.o(.text.PID_Init) refers to pid.o(.text.pid_set_target) for pid_set_target
    pid_app.o(.ARM.exidx.text.PID_Init) refers to pid_app.o(.text.PID_Init) for [Anonymous Symbol]
    pid_app.o(.text.Line_PID_control) refers to gray_app.o(.bss..L_MergedGlobals) for g_line_position_error
    pid_app.o(.text.Line_PID_control) refers to pid_app.o(.bss..L_MergedGlobals.2) for .L_MergedGlobals.2
    pid_app.o(.text.Line_PID_control) refers to pid.o(.text.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(.text.Line_PID_control) refers to pid_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    pid_app.o(.text.Line_PID_control) refers to pid.o(.text.pid_constrain) for pid_constrain
    pid_app.o(.text.Line_PID_control) refers to pid_app.o(.bss..L_MergedGlobals.1) for .L_MergedGlobals.1
    pid_app.o(.text.Line_PID_control) refers to pid.o(.text.pid_set_target) for pid_set_target
    pid_app.o(.ARM.exidx.text.Line_PID_control) refers to pid_app.o(.text.Line_PID_control) for [Anonymous Symbol]
    pid_app.o(.text.Angle_PID_control) refers to gray_app.o(.bss..L_MergedGlobals) for g_line_position_error
    pid_app.o(.text.Angle_PID_control) refers to pid_app.o(.bss..L_MergedGlobals.2) for .L_MergedGlobals.2
    pid_app.o(.text.Angle_PID_control) refers to pid.o(.text.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(.text.Angle_PID_control) refers to pid_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    pid_app.o(.text.Angle_PID_control) refers to pid.o(.text.pid_constrain) for pid_constrain
    pid_app.o(.text.Angle_PID_control) refers to pid_app.o(.bss..L_MergedGlobals.1) for .L_MergedGlobals.1
    pid_app.o(.text.Angle_PID_control) refers to pid.o(.text.pid_set_target) for pid_set_target
    pid_app.o(.ARM.exidx.text.Angle_PID_control) refers to pid_app.o(.text.Angle_PID_control) for [Anonymous Symbol]
    pid_app.o(.text.PID_Task) refers to pid_app.o(.bss..L_MergedGlobals.1) for .L_MergedGlobals.1
    pid_app.o(.text.PID_Task) refers to pid_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    pid_app.o(.text.PID_Task) refers to pid_app.o(.bss..L_MergedGlobals.2) for .L_MergedGlobals.2
    pid_app.o(.text.PID_Task) refers to scheduler_task.o(.bss.distance) for distance
    pid_app.o(.text.PID_Task) refers to gray_app.o(.bss..L_MergedGlobals) for g_line_position_error
    pid_app.o(.text.PID_Task) refers to pid.o(.text.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(.text.PID_Task) refers to pid.o(.text.pid_constrain) for pid_constrain
    pid_app.o(.text.PID_Task) refers to pid.o(.text.pid_set_target) for pid_set_target
    pid_app.o(.text.PID_Task) refers to encoder_app.o(.bss..L_MergedGlobals) for left_encoder
    pid_app.o(.text.PID_Task) refers to motor_app.o(.bss..L_MergedGlobals) for right_motor
    pid_app.o(.text.PID_Task) refers to motor_driver.o(.text.Motor_SetSpeed) for Motor_SetSpeed
    pid_app.o(.ARM.exidx.text.PID_Task) refers to pid_app.o(.text.PID_Task) for [Anonymous Symbol]
    jy901s_app.o(.text.jy901s_task) refers to jy901s_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    jy901s_app.o(.text.jy901s_task) refers to usart.o(.bss.huart5) for huart5
    jy901s_app.o(.text.jy901s_task) refers to uart_app.o(.text.my_printf) for my_printf
    jy901s_app.o(.text.jy901s_task) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    jy901s_app.o(.text.jy901s_task) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    jy901s_app.o(.text.jy901s_task) refers to jy901s_app.o(.bss.jy901s) for jy901s
    jy901s_app.o(.text.jy901s_task) refers to jy901s_driver.o(.text.JY901S_WatchdogCheck) for JY901S_WatchdogCheck
    jy901s_app.o(.text.jy901s_task) refers to jy901s_driver.o(.text.JY901S_GetData) for JY901S_GetData
    jy901s_app.o(.ARM.exidx.text.jy901s_task) refers to jy901s_app.o(.text.jy901s_task) for [Anonymous Symbol]
    jy901s_app.o(.text.HAL_UART_RxCpltCallback) refers to jy901s_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    jy901s_app.o(.text.HAL_UART_RxCpltCallback) refers to jy901s_app.o(.bss.jy901s) for jy901s
    jy901s_app.o(.text.HAL_UART_RxCpltCallback) refers to jy901s_driver.o(.text.JY901S_ProcessBuffer) for JY901S_ProcessBuffer
    jy901s_app.o(.text.HAL_UART_RxCpltCallback) refers to usart.o(.bss.huart5) for huart5
    jy901s_app.o(.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    jy901s_app.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to jy901s_app.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    jy901s_app.o(.text.jy901s_init) refers to jy901s_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    jy901s_app.o(.text.jy901s_init) refers to usart.o(.bss.huart5) for huart5
    jy901s_app.o(.text.jy901s_init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    jy901s_app.o(.text.jy901s_init) refers to jy901s_app.o(.bss.jy901s) for jy901s
    jy901s_app.o(.text.jy901s_init) refers to jy901s_driver.o(.text.JY901S_Create) for JY901S_Create
    jy901s_app.o(.ARM.exidx.text.jy901s_init) refers to jy901s_app.o(.text.jy901s_init) for [Anonymous Symbol]
    scheduler.o(.text.Scheduler_Init) refers to scheduler_task.o(.text.System_Init) for System_Init
    scheduler.o(.text.Scheduler_Init) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.ARM.exidx.text.Scheduler_Init) refers to scheduler.o(.text.Scheduler_Init) for [Anonymous Symbol]
    scheduler.o(.text.Scheduler_Run) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.text.Scheduler_Run) refers to scheduler.o(.data.scheduler_task) for scheduler_task
    scheduler.o(.text.Scheduler_Run) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    scheduler.o(.ARM.exidx.text.Scheduler_Run) refers to scheduler.o(.text.Scheduler_Run) for [Anonymous Symbol]
    scheduler.o(.data.scheduler_task) refers to btn_app.o(.text.btn_task) for btn_task
    scheduler.o(.data.scheduler_task) refers to oled_app.o(.text.Oled_Task) for Oled_Task
    scheduler.o(.data.scheduler_task) refers to led_app.o(.text.Led_Task) for Led_Task
    scheduler.o(.data.scheduler_task) refers to uart_app.o(.text.uart_task) for uart_task
    scheduler.o(.data.scheduler_task) refers to encoder_app.o(.text.Encoder_Task) for Encoder_Task
    scheduler_task.o(.text.System_Init) refers to led_app.o(.text.Led_Init) for Led_Init
    scheduler_task.o(.text.System_Init) refers to motor_app.o(.text.Motor_Init) for Motor_Init
    scheduler_task.o(.text.System_Init) refers to btn_app.o(.text.app_btn_init) for app_btn_init
    scheduler_task.o(.text.System_Init) refers to oled_app.o(.text.Oled_Init) for Oled_Init
    scheduler_task.o(.text.System_Init) refers to uart_app.o(.text.uart_init) for uart_init
    scheduler_task.o(.text.System_Init) refers to gray_app.o(.text.Gray_Init) for Gray_Init
    scheduler_task.o(.text.System_Init) refers to encoder_app.o(.text.Encoder_Init) for Encoder_Init
    scheduler_task.o(.text.System_Init) refers to jy901s_app.o(.text.jy901s_init) for jy901s_init
    scheduler_task.o(.text.System_Init) refers to pid_app.o(.text.PID_Init) for PID_Init
    scheduler_task.o(.text.System_Init) refers to usart.o(.bss.huart1) for huart1
    scheduler_task.o(.text.System_Init) refers to uart_app.o(.text.my_printf) for my_printf
    scheduler_task.o(.text.System_Init) refers to tim.o(.bss.htim2) for htim2
    scheduler_task.o(.text.System_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    scheduler_task.o(.ARM.exidx.text.System_Init) refers to scheduler_task.o(.text.System_Init) for [Anonymous Symbol]
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    roundf.o(i.__hardfp_roundf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    roundf.o(i.__hardfp_roundf) refers to frnd.o(x$fpl$frnd) for _frnd
    roundf.o(i.roundf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    roundf.o(i.roundf) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _c16rtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frnd.o(x$fpl$frnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frnd.o(x$fpl$frnd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec_accurate.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec_accurate.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec_accurate.o(.text) refers to btod_accurate.o(.text) for _btod_main
    _printf_fp_dec_accurate.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec_accurate.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _c16rtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod_accurate.o(.text) refers to btod_accurate_common.o(.text) for _btod_common
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7em.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    btod_accurate_common.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7em.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000011) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.MX_GPIO_Init), (8 bytes).
    Removing dma.o(.text), (0 bytes).
    Removing dma.o(.ARM.exidx.text.MX_DMA_Init), (8 bytes).
    Removing i2c.o(.text), (0 bytes).
    Removing i2c.o(.ARM.exidx.text.MX_I2C1_Init), (8 bytes).
    Removing i2c.o(.ARM.exidx.text.MX_I2C2_Init), (8 bytes).
    Removing i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing i2c.o(.text.HAL_I2C_MspDeInit), (104 bytes).
    Removing i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing tim.o(.text), (0 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM1_Init), (8 bytes).
    Removing tim.o(.text.HAL_TIM_MspPostInit), (92 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_MspPostInit), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM2_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM3_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM4_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM6_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Base_MspDeInit), (94 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Encoder_MspDeInit), (90 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.MX_UART4_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_UART5_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART1_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART2_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing usart.o(.text.HAL_UART_MspDeInit), (226 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_it.o(.text), (0 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream0_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.TIM2_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.I2C1_EV_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.I2C2_EV_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UART5_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream2_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_msp.o(.text), (0 bytes).
    Removing stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text), (0 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit), (52 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit), (476 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive), (796 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit), (378 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT), (376 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA), (472 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAError), (76 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA), (472 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA), (276 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA), (276 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read), (600 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT), (260 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT), (272 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA), (518 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA), (580 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady), (532 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT), (324 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA), (598 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA), (448 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT), (54 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT), (88 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler), (336 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (168 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (150 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (62 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig), (330 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (50 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (88 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S), (106 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit), (296 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program), (218 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (250 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (240 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (312 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock), (46 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (42 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.bss.pFlash), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (394 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector), (70 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (184 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (56 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (462 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (44 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (22 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart), (114 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT), (1236 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit), (136 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start), (114 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (454 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (46 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (156 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (130 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (84 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling), (102 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (88 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f4xx_hal.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DeInit), (68 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SetTickFreq), (38 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text), (0 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (186 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (148 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (140 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (42 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start), (162 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop), (52 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT), (60 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA), (256 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt), (20 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAError), (94 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA), (72 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start), (270 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd), (36 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop), (170 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT), (330 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA), (634 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt), (106 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop), (170 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT), (330 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA), (634 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start), (294 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop), (126 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT), (354 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT), (150 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA), (594 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt), (118 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA), (162 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start), (124 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop), (140 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT), (140 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT), (156 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop), (130 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT), (230 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT), (214 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA), (552 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA), (230 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel), (436 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig), (104 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel), (454 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig), (174 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel), (578 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart), (644 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt), (20 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop), (118 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart), (628 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop), (118 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent), (36 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear), (212 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig), (22 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro), (88 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig), (250 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT), (88 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue), (22 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.rodata.cst16), (48 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init), (200 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start), (222 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop), (66 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT), (230 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA), (282 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start), (254 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop), (146 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT), (274 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA), (530 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt), (82 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA), (176 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start), (254 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop), (146 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT), (274 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA), (530 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA), (176 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start), (126 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop), (128 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT), (142 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA), (154 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt), (12 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt), (12 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text), (0 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init), (104 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_Init), (120 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init), (128 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive), (558 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT), (84 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA), (212 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt), (176 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA), (336 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause), (342 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume), (348 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle), (478 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT), (200 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort), (482 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit), (208 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive), (366 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT), (516 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT), (214 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback), (16 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT), (372 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback), (18 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak), (114 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode), (116 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode), (116 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter), (44 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver), (46 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetState), (14 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt), (8 bytes).
    Removing system_stm32f4xx.o(.text), (0 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f4xx.o(.text.SystemCoreClockUpdate), (134 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing ringbuffer.o(.text), (0 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_status), (20 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_status), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_init), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_data_len), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_put_force), (208 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put_force), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_get), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_peek), (132 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_peek), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_putchar), (100 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_putchar_force), (102 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar_force), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_getchar), (116 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_getchar), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_reset), (6 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_reset), (8 bytes).
    Removing hardware_iic.o(.text), (0 bytes).
    Removing hardware_iic.o(.text.IIC_ReadByte), (40 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_ReadByte), (8 bytes).
    Removing hardware_iic.o(.text.IIC_ReadBytes), (48 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_ReadBytes), (8 bytes).
    Removing hardware_iic.o(.text.IIC_WriteByte), (52 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_WriteByte), (8 bytes).
    Removing hardware_iic.o(.text.IIC_WriteBytes), (48 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_WriteBytes), (8 bytes).
    Removing hardware_iic.o(.text.Ping), (54 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.Ping), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Digtal), (48 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Digtal), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Anolog), (44 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Anolog), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Single_Anolog), (50 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Single_Anolog), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Anolog_Normalize), (54 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Anolog_Normalize), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Offset), (54 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Offset), (8 bytes).
    Removing ebtn.o(.text), (0 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_init), (8 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_process_with_curr_state), (8 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_process), (8 bytes).
    Removing ebtn.o(.text.ebtn_get_total_btn_cnt), (52 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_get_total_btn_cnt), (8 bytes).
    Removing ebtn.o(.text.ebtn_get_btn_index_by_key_id), (160 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_get_btn_index_by_key_id), (8 bytes).
    Removing ebtn.o(.text.ebtn_get_btn_by_key_id), (178 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_get_btn_by_key_id), (8 bytes).
    Removing ebtn.o(.text.ebtn_get_btn_index_by_btn), (160 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_get_btn_index_by_btn), (8 bytes).
    Removing ebtn.o(.text.ebtn_get_btn_index_by_btn_dyn), (160 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_get_btn_index_by_btn_dyn), (8 bytes).
    Removing ebtn.o(.text.ebtn_combo_btn_add_btn_by_idx), (26 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_combo_btn_add_btn_by_idx), (8 bytes).
    Removing ebtn.o(.text.ebtn_combo_btn_remove_btn_by_idx), (28 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_combo_btn_remove_btn_by_idx), (8 bytes).
    Removing ebtn.o(.text.ebtn_combo_btn_add_btn), (182 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_combo_btn_add_btn), (8 bytes).
    Removing ebtn.o(.text.ebtn_combo_btn_remove_btn), (184 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_combo_btn_remove_btn), (8 bytes).
    Removing ebtn.o(.text.ebtn_is_btn_active), (14 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_is_btn_active), (8 bytes).
    Removing ebtn.o(.text.ebtn_is_btn_in_process), (14 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_is_btn_in_process), (8 bytes).
    Removing ebtn.o(.text.ebtn_is_in_process), (268 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_is_in_process), (8 bytes).
    Removing ebtn.o(.text.ebtn_register), (140 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_register), (8 bytes).
    Removing ebtn.o(.text.ebtn_combo_register), (72 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_combo_register), (8 bytes).
    Removing ebtn.o(.text.ebtn_set_config), (14 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_set_config), (8 bytes).
    Removing ebtn.o(.text.ebtn_get_config), (14 bytes).
    Removing ebtn.o(.ARM.exidx.text.ebtn_get_config), (8 bytes).
    Removing ebtn.o(.ARM.exidx.text.prv_process_btn), (8 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.text.OLED_Write_cmd), (48 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Write_cmd), (8 bytes).
    Removing oled.o(.text.OLED_Write_data), (48 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Write_data), (8 bytes).
    Removing oled.o(.text.OLED_ShowPic), (340 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowPic), (8 bytes).
    Removing oled.o(.text.OLED_Set_Position), (114 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Set_Position), (8 bytes).
    Removing oled.o(.text.OLED_ShowHanzi), (1112 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowHanzi), (8 bytes).
    Removing oled.o(.text.OLED_ShowHzbig), (578 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowHzbig), (8 bytes).
    Removing oled.o(.text.OLED_ShowFloat), (364 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowFloat), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing oled.o(.text.OLED_ShowNum), (280 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowNum), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowStr), (8 bytes).
    Removing oled.o(.text.OLED_Allfill), (536 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Allfill), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing oled.o(.text.OLED_Display_On), (106 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Display_On), (8 bytes).
    Removing oled.o(.text.OLED_Display_Off), (106 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Display_Off), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled.o(.rodata.Hzk), (128 bytes).
    Removing oled.o(.rodata.Hzb), (512 bytes).
    Removing pid.o(.text), (0 bytes).
    Removing pid.o(.ARM.exidx.text.pid_init), (8 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_target), (8 bytes).
    Removing pid.o(.text.pid_set_params), (6 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_params), (8 bytes).
    Removing pid.o(.text.pid_set_limit), (6 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_limit), (8 bytes).
    Removing pid.o(.text.pid_reset), (18 bytes).
    Removing pid.o(.ARM.exidx.text.pid_reset), (8 bytes).
    Removing pid.o(.text.pid_calculate_positional), (128 bytes).
    Removing pid.o(.ARM.exidx.text.pid_calculate_positional), (8 bytes).
    Removing pid.o(.text.pid_calculate_incremental), (144 bytes).
    Removing pid.o(.ARM.exidx.text.pid_calculate_incremental), (8 bytes).
    Removing pid.o(.text.pid_constrain), (30 bytes).
    Removing pid.o(.ARM.exidx.text.pid_constrain), (8 bytes).
    Removing pid.o(.text.pid_app_limit_integral), (40 bytes).
    Removing pid.o(.ARM.exidx.text.pid_app_limit_integral), (8 bytes).
    Removing oled_driver.o(.text), (0 bytes).
    Removing oled_driver.o(.ARM.exidx.text.oled_printf), (8 bytes).
    Removing led_driver.o(.text), (0 bytes).
    Removing led_driver.o(.ARM.exidx.text.Led_Display), (8 bytes).
    Removing motor_driver.o(.text), (0 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Create), (8 bytes).
    Removing motor_driver.o(.text.DRV8871_Control), (564 bytes).
    Removing motor_driver.o(.ARM.exidx.text.DRV8871_Control), (8 bytes).
    Removing motor_driver.o(.text.Motor_SetSpeed), (228 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_SetSpeed), (8 bytes).
    Removing motor_driver.o(.text.Motor_Stop), (168 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Stop), (8 bytes).
    Removing motor_driver.o(.text.Motor_GetState), (26 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_GetState), (8 bytes).
    Removing motor_driver.o(.text.Motor_Enable), (176 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Enable), (8 bytes).
    Removing motor_driver.o(.text.Motor_SetDecayMode), (200 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_SetDecayMode), (8 bytes).
    Removing encoder_driver.o(.text), (0 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.Encoder_Driver_Init), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.Encoder_Driver_Update), (8 bytes).
    Removing jy901s_driver.o(.text), (0 bytes).
    Removing jy901s_driver.o(.text.jy901s_set), (688 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.jy901s_set), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_SetBaudRate), (160 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_SetBaudRate), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_SetOutputRate), (138 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_SetOutputRate), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_SetContent), (164 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_SetContent), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_SetBandwidth), (160 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_SetBandwidth), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_WatchdogInit), (62 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_WatchdogInit), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_WatchdogEnable), (54 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_WatchdogEnable), (8 bytes).
    Removing jy901s_driver.o(.text.jy901s_watchdog_init), (264 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.jy901s_watchdog_init), (8 bytes).
    Removing jy901s_driver.o(.text.jy901s_calibration), (1196 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.jy901s_calibration), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_StartAccCalibration), (144 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_StartAccCalibration), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_StopAccCalibration), (144 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_StopAccCalibration), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_StartMagCalibration), (144 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_StartMagCalibration), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_StopMagCalibration), (144 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_StopMagCalibration), (8 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_Create), (8 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_ProcessBuffer), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_WatchdogFeed), (22 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_WatchdogFeed), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetState), (18 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetState), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_Enable), (78 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_Enable), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetGyroZ), (40 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetGyroZ), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetGyroX), (40 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetGyroX), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetGyroY), (40 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetGyroY), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetYaw), (40 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetYaw), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetRoll), (40 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetRoll), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetPitch), (40 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetPitch), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetData), (32 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetData), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetAccX), (40 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetAccX), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetAccY), (40 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetAccY), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetAccZ), (40 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetAccZ), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetTemperature), (40 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetTemperature), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetMagX), (40 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetMagX), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetMagY), (40 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetMagY), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetMagZ), (40 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetMagZ), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_SaveConfig), (50 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_SaveConfig), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_AutoScanBaudRate), (212 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_AutoScanBaudRate), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_ReadRegister), (46 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_ReadRegister), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_StartManualCalibration), (140 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_StartManualCalibration), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_StopManualCalibration), (140 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_StopManualCalibration), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_ResetYaw), (140 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_ResetYaw), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_WatchdogCheck), (228 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_WatchdogCheck), (8 bytes).
    Removing jy901s_driver.o(.text.JY901S_GetRestartCount), (10 bytes).
    Removing jy901s_driver.o(.ARM.exidx.text.JY901S_GetRestartCount), (8 bytes).
    Removing jy901s_driver.o(.rodata.str1.1), (1117 bytes).
    Removing jy901s_driver.o(.rodata..L.str.35), (6 bytes).
    Removing oled_app.o(.text), (0 bytes).
    Removing oled_app.o(.ARM.exidx.text.Oled_Init), (8 bytes).
    Removing oled_app.o(.ARM.exidx.text.Oled_Task), (8 bytes).
    Removing led_app.o(.text), (0 bytes).
    Removing led_app.o(.ARM.exidx.text.Led_Init), (8 bytes).
    Removing led_app.o(.ARM.exidx.text.Led_Task), (8 bytes).
    Removing btn_app.o(.text), (0 bytes).
    Removing btn_app.o(.ARM.exidx.text.prv_btn_get_state), (8 bytes).
    Removing btn_app.o(.ARM.exidx.text.prv_btn_event), (8 bytes).
    Removing btn_app.o(.ARM.exidx.text.app_btn_init), (8 bytes).
    Removing btn_app.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing btn_app.o(.ARM.exidx.text.btn_task), (8 bytes).
    Removing uart_app.o(.text), (0 bytes).
    Removing uart_app.o(.ARM.exidx.text.my_printf), (8 bytes).
    Removing uart_app.o(.ARM.exidx.text.uart_init), (8 bytes).
    Removing uart_app.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing uart_app.o(.ARM.exidx.text.uart_task), (8 bytes).
    Removing uart_app.o(.bss.cam_rx_dma_buffer), (64 bytes).
    Removing uart_app.o(.bss.cam_out_buffer), (64 bytes).
    Removing uart_app.o(.bss.cam_ringbuffer), (12 bytes).
    Removing uart_app.o(.bss.cam_ringbuffer_pool), (64 bytes).
    Removing motor_app.o(.text), (0 bytes).
    Removing motor_app.o(.ARM.exidx.text.Motor_Init), (8 bytes).
    Removing motor_app.o(.text.motor_set_l), (12 bytes).
    Removing motor_app.o(.ARM.exidx.text.motor_set_l), (8 bytes).
    Removing motor_app.o(.text.motor_set_r), (14 bytes).
    Removing motor_app.o(.ARM.exidx.text.motor_set_r), (8 bytes).
    Removing motor_app.o(.text.motor_break), (28 bytes).
    Removing motor_app.o(.ARM.exidx.text.motor_break), (8 bytes).
    Removing encoder_app.o(.text), (0 bytes).
    Removing encoder_app.o(.ARM.exidx.text.Encoder_Init), (8 bytes).
    Removing encoder_app.o(.ARM.exidx.text.Encoder_Task), (8 bytes).
    Removing gray_app.o(.text), (0 bytes).
    Removing gray_app.o(.ARM.exidx.text.Gray_Init), (8 bytes).
    Removing gray_app.o(.text.Gray_Task), (252 bytes).
    Removing gray_app.o(.ARM.exidx.text.Gray_Task), (8 bytes).
    Removing gray_app.o(.data.gray_weights), (32 bytes).
    Removing pid_app.o(.text), (0 bytes).
    Removing pid_app.o(.ARM.exidx.text.PID_Init), (8 bytes).
    Removing pid_app.o(.text.Line_PID_control), (112 bytes).
    Removing pid_app.o(.ARM.exidx.text.Line_PID_control), (8 bytes).
    Removing pid_app.o(.text.Angle_PID_control), (114 bytes).
    Removing pid_app.o(.ARM.exidx.text.Angle_PID_control), (8 bytes).
    Removing pid_app.o(.text.PID_Task), (312 bytes).
    Removing pid_app.o(.ARM.exidx.text.PID_Task), (8 bytes).
    Removing jy901s_app.o(.text), (0 bytes).
    Removing jy901s_app.o(.text.jy901s_task), (108 bytes).
    Removing jy901s_app.o(.ARM.exidx.text.jy901s_task), (8 bytes).
    Removing jy901s_app.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing jy901s_app.o(.ARM.exidx.text.jy901s_init), (8 bytes).
    Removing scheduler.o(.text), (0 bytes).
    Removing scheduler.o(.ARM.exidx.text.Scheduler_Init), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.Scheduler_Run), (8 bytes).
    Removing scheduler_task.o(.text), (0 bytes).
    Removing scheduler_task.o(.ARM.exidx.text.System_Init), (8 bytes).
    Removing scheduler_task.o(.bss.point_count), (1 bytes).
    Removing scheduler_task.o(.data.system_mode), (1 bytes).
    Removing scheduler_task.o(.bss.circle_count), (1 bytes).
    Removing scheduler_task.o(.bss.distance), (4 bytes).
    Removing scheduler_task.o(.bss.measure_timer5ms), (1 bytes).
    Removing scheduler_task.o(.bss.key_timer10ms), (1 bytes).
    Removing scheduler_task.o(.bss.output_ff_flag), (1 bytes).
    Removing scheduler_task.o(.bss.intput_timer500ms), (4 bytes).
    Removing scheduler_task.o(.bss.intput_ff_flag), (1 bytes).
    Removing scheduler_task.o(.bss.output_timer500ms), (4 bytes).
    Removing scheduler_task.o(.bss.led_timer500ms), (4 bytes).

1119 unused section(s) (total 67100 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate_common.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _c16rtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7em.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec_accurate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frnd.s                          0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  roundf.o ABSOLUTE
    Led_driver.c                             0x00000000   Number         0  led_driver.o ABSOLUTE
    Scheduler.c                              0x00000000   Number         0  scheduler.o ABSOLUTE
    Scheduler_Task.c                         0x00000000   Number         0  scheduler_task.o ABSOLUTE
    btn_app.c                                0x00000000   Number         0  btn_app.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dma.c                                    0x00000000   Number         0  dma.o ABSOLUTE
    ebtn.c                                   0x00000000   Number         0  ebtn.o ABSOLUTE
    encoder_app.c                            0x00000000   Number         0  encoder_app.o ABSOLUTE
    encoder_driver.c                         0x00000000   Number         0  encoder_driver.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    gray_app.c                               0x00000000   Number         0  gray_app.o ABSOLUTE
    hardware_iic.c                           0x00000000   Number         0  hardware_iic.o ABSOLUTE
    i2c.c                                    0x00000000   Number         0  i2c.o ABSOLUTE
    jy901s_app.c                             0x00000000   Number         0  jy901s_app.o ABSOLUTE
    jy901s_driver.c                          0x00000000   Number         0  jy901s_driver.o ABSOLUTE
    led_app.c                                0x00000000   Number         0  led_app.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    motor_app.c                              0x00000000   Number         0  motor_app.o ABSOLUTE
    motor_driver.c                           0x00000000   Number         0  motor_driver.o ABSOLUTE
    oled.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    oled_app.c                               0x00000000   Number         0  oled_app.o ABSOLUTE
    oled_driver.c                            0x00000000   Number         0  oled_driver.o ABSOLUTE
    pid.c                                    0x00000000   Number         0  pid.o ABSOLUTE
    pid_app.c                                0x00000000   Number         0  pid_app.o ABSOLUTE
    ringbuffer.c                             0x00000000   Number         0  ringbuffer.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    stm32f4xx_hal.c                          0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    stm32f4xx_hal_cortex.c                   0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    stm32f4xx_hal_dma.c                      0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    stm32f4xx_hal_dma_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    stm32f4xx_hal_exti.c                     0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    stm32f4xx_hal_flash.c                    0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    stm32f4xx_hal_flash_ex.c                 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    stm32f4xx_hal_flash_ramfunc.c            0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    stm32f4xx_hal_gpio.c                     0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    stm32f4xx_hal_i2c.c                      0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    stm32f4xx_hal_i2c_ex.c                   0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    stm32f4xx_hal_msp.c                      0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    stm32f4xx_hal_pwr.c                      0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    stm32f4xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    stm32f4xx_hal_rcc.c                      0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    stm32f4xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    stm32f4xx_hal_tim.c                      0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    stm32f4xx_hal_tim_ex.c                   0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    stm32f4xx_hal_uart.c                     0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    tim.c                                    0x00000000   Number         0  tim.o ABSOLUTE
    uart_app.c                               0x00000000   Number         0  uart_app.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       92  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001ec   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_null                           0x08000248   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x0800024c   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000268   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x08000268   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x0800026e   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000274   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800027a   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000280   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x08000286   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x0800028c   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x08000296   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x0800029c   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x080002a2   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x080002a8   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x080002ae   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x080002b4   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x080002ba   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x080002c0   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x080002c6   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x080002cc   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x080002d2   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x080002dc   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080002e2   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080002e8   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080002ee   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080002f4   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080002f8   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080002fa   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080002fe   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x080002fe   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x080002fe   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080002fe   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x080002fe   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000011          0x080002fe   Section        6  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000304   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000014          0x08000304   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000014)
    .ARM.Collect$$libinit$$00000015          0x08000310   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000310   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000018          0x08000310   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000018)
    .ARM.Collect$$libinit$$00000019          0x0800031a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800031a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800031a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800031a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800031a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800031a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800031a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x0800031a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x0800031a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800031a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800031a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x0800031a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x0800031a   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x0800031c   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800031e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800031e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0800031e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0800031e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0800031e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0800031e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0800031e   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000320   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000320   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000320   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000326   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000326   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800032a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800032a   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000332   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000334   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000334   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000338   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    $v0                                      0x08000340   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000340   Section       64  startup_stm32f407xx.o(.text)
    .text                                    0x08000380   Section      240  lludivv7m.o(.text)
    .text                                    0x08000470   Section        0  vsnprintf.o(.text)
    .text                                    0x080004ac   Section        0  memcmp.o(.text)
    .text                                    0x08000504   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x0800058e   Section       68  rt_memclr.o(.text)
    .text                                    0x080005d2   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000620   Section        0  heapauxi.o(.text)
    .text                                    0x08000626   Section        0  _printf_pad.o(.text)
    .text                                    0x08000674   Section        0  _printf_truncate.o(.text)
    .text                                    0x08000698   Section        0  _printf_str.o(.text)
    .text                                    0x080006ec   Section        0  _printf_dec.o(.text)
    .text                                    0x08000764   Section        0  _printf_charcount.o(.text)
    _printf_input_char                       0x0800078d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x0800078c   Section        0  _printf_char_common.o(.text)
    .text                                    0x080007bc   Section        0  _snputc.o(.text)
    .text                                    0x080007cc   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08000888   Section        0  _printf_longlong_dec.o(.text)
    _printf_longlong_oct_internal            0x08000905   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000904   Section        0  _printf_oct_int_ll.o(.text)
    _printf_hex_common                       0x08000975   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000974   Section        0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000a08   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000b90   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000bf4   Section      138  lludiv10.o(.text)
    .text                                    0x08000c7e   Section        0  _printf_intcommon.o(.text)
    _fp_digits                               0x08000d31   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000d30   Section        0  _printf_fp_dec.o(.text)
    .text                                    0x0800114c   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001448   Section        0  _printf_char.o(.text)
    .text                                    0x08001474   Section        0  _printf_wchar.o(.text)
    .text                                    0x080014a0   Section        0  _c16rtomb.o(.text)
    .text                                    0x080014e8   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001534   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08001544   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x0800154c   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080015cc   Section        0  bigflt0.o(.text)
    .text                                    0x080016b0   Section        0  exit.o(.text)
    .text                                    0x080016c4   Section        8  libspace.o(.text)
    .text                                    0x080016d0   Section      124  strcmpv7em.o(.text)
    .text                                    0x0800174c   Section        0  sys_exit.o(.text)
    .text                                    0x08001758   Section        2  use_no_semi.o(.text)
    .text                                    0x0800175a   Section        0  indicate_semi.o(.text)
    [Anonymous Symbol]                       0x0800175c   Section        0  stm32f4xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x08001760   Section        0  stm32f4xx_it.o(.text.DMA1_Stream0_IRQHandler)
    [Anonymous Symbol]                       0x0800176c   Section        0  stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler)
    [Anonymous Symbol]                       0x08001778   Section        0  stm32f4xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x0800177c   Section        0  encoder_driver.o(.text.Encoder_Driver_Init)
    [Anonymous Symbol]                       0x080017a0   Section        0  encoder_driver.o(.text.Encoder_Driver_Update)
    [Anonymous Symbol]                       0x08001808   Section        0  encoder_app.o(.text.Encoder_Init)
    [Anonymous Symbol]                       0x08001838   Section        0  encoder_app.o(.text.Encoder_Task)
    [Anonymous Symbol]                       0x08001890   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x08001898   Section        0  gray_app.o(.text.Gray_Init)
    [Anonymous Symbol]                       0x0800189c   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    [Anonymous Symbol]                       0x0800192c   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    [Anonymous Symbol]                       0x08001950   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState)
    [Anonymous Symbol]                       0x08001958   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    [Anonymous Symbol]                       0x08001b1c   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    [Anonymous Symbol]                       0x08001c80   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    [Anonymous Symbol]                       0x08001d24   Section        0  stm32f4xx_hal.o(.text.HAL_Delay)
    [Anonymous Symbol]                       0x08001d4c   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x08001eec   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    [Anonymous Symbol]                       0x08001ef8   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x08001f04   Section        0  stm32f4xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x08001f10   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback)
    [Anonymous Symbol]                       0x08001f14   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback)
    [Anonymous Symbol]                       0x08001f18   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler)
    [Anonymous Symbol]                       0x08002514   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback)
    [Anonymous Symbol]                       0x08002518   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init)
    [Anonymous Symbol]                       0x0800267c   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback)
    [Anonymous Symbol]                       0x08002680   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback)
    [Anonymous Symbol]                       0x08002684   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback)
    [Anonymous Symbol]                       0x08002688   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback)
    [Anonymous Symbol]                       0x0800268c   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback)
    [Anonymous Symbol]                       0x08002690   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write)
    [Anonymous Symbol]                       0x080027ec   Section        0  i2c.o(.text.HAL_I2C_MspInit)
    [Anonymous Symbol]                       0x080028d0   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback)
    [Anonymous Symbol]                       0x080028d4   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback)
    [Anonymous Symbol]                       0x080028d8   Section        0  stm32f4xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x080028f4   Section        0  stm32f4xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x0800292c   Section        0  stm32f4xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08002974   Section        0  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x080029ac   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x080029d0   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x08002a28   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08002a48   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x08002bac   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    [Anonymous Symbol]                       0x08002bd4   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    [Anonymous Symbol]                       0x08002bfc   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x08002c68   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x08003014   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08003040   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback)
    [Anonymous Symbol]                       0x08003044   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback)
    [Anonymous Symbol]                       0x08003048   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime)
    [Anonymous Symbol]                       0x08003094   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    [Anonymous Symbol]                       0x08003150   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    [Anonymous Symbol]                       0x080031ac   Section        0  tim.o(.text.HAL_TIM_Base_MspInit)
    [Anonymous Symbol]                       0x08003248   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT)
    [Anonymous Symbol]                       0x080032f4   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    [Anonymous Symbol]                       0x08003494   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init)
    [Anonymous Symbol]                       0x08003548   Section        0  tim.o(.text.HAL_TIM_Encoder_MspInit)
    [Anonymous Symbol]                       0x08003608   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start)
    [Anonymous Symbol]                       0x080036c4   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback)
    [Anonymous Symbol]                       0x080036c8   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler)
    [Anonymous Symbol]                       0x08003818   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback)
    [Anonymous Symbol]                       0x0800381c   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    [Anonymous Symbol]                       0x08003a3c   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    [Anonymous Symbol]                       0x08003a98   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit)
    [Anonymous Symbol]                       0x08003a9c   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback)
    [Anonymous Symbol]                       0x08003aa0   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start)
    [Anonymous Symbol]                       0x08003bb0   Section        0  btn_app.o(.text.HAL_TIM_PeriodElapsedCallback)
    [Anonymous Symbol]                       0x08003bb4   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback)
    [Anonymous Symbol]                       0x08003bb8   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
    [Anonymous Symbol]                       0x08003da8   Section        0  uart_app.o(.text.HAL_UARTEx_RxEventCallback)
    [Anonymous Symbol]                       0x08003e04   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop)
    [Anonymous Symbol]                       0x08004020   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    [Anonymous Symbol]                       0x08004024   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    [Anonymous Symbol]                       0x080045b8   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    [Anonymous Symbol]                       0x08004618   Section        0  usart.o(.text.HAL_UART_MspInit)
    [Anonymous Symbol]                       0x08004898   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA)
    [Anonymous Symbol]                       0x08004a18   Section        0  jy901s_app.o(.text.HAL_UART_RxCpltCallback)
    [Anonymous Symbol]                       0x08004a5c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    [Anonymous Symbol]                       0x08004a60   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    [Anonymous Symbol]                       0x08004bf4   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    [Anonymous Symbol]                       0x08004bf8   Section        0  stm32f4xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x08004bfc   Section        0  stm32f4xx_it.o(.text.I2C1_EV_IRQHandler)
    [Anonymous Symbol]                       0x08004c08   Section        0  stm32f4xx_it.o(.text.I2C2_EV_IRQHandler)
    I2C_DMAAbort                             0x08004c15   Thumb Code   290  stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort)
    [Anonymous Symbol]                       0x08004c14   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort)
    I2C_ITError                              0x08004d39   Thumb Code   396  stm32f4xx_hal_i2c.o(.text.I2C_ITError)
    [Anonymous Symbol]                       0x08004d38   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_ITError)
    I2C_MasterReceive_BTF                    0x08004ec5   Thumb Code   248  stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF)
    [Anonymous Symbol]                       0x08004ec4   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF)
    I2C_MasterReceive_RXNE                   0x08004fbd   Thumb Code   326  stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE)
    [Anonymous Symbol]                       0x08004fbc   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE)
    I2C_MasterTransmit_BTF                   0x08005105   Thumb Code   130  stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF)
    [Anonymous Symbol]                       0x08005104   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF)
    I2C_MasterTransmit_TXE                   0x08005189   Thumb Code   178  stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE)
    [Anonymous Symbol]                       0x08005188   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE)
    I2C_MemoryTransmit_TXE_BTF               0x0800523d   Thumb Code   190  stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF)
    [Anonymous Symbol]                       0x0800523c   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF)
    I2C_RequestMemoryWrite                   0x080052fd   Thumb Code   200  stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite)
    [Anonymous Symbol]                       0x080052fc   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite)
    I2C_WaitOnBTFFlagUntilTimeout            0x080053c5   Thumb Code   186  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout)
    [Anonymous Symbol]                       0x080053c4   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08005481   Thumb Code   458  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout)
    [Anonymous Symbol]                       0x08005480   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x0800564d   Thumb Code   214  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout)
    [Anonymous Symbol]                       0x0800564c   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x08005725   Thumb Code   186  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout)
    [Anonymous Symbol]                       0x08005724   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout)
    [Anonymous Symbol]                       0x080057e0   Section        0  jy901s_driver.o(.text.JY901S_Create)
    [Anonymous Symbol]                       0x08005850   Section        0  jy901s_driver.o(.text.JY901S_ProcessBuffer)
    [Anonymous Symbol]                       0x08005b48   Section        0  led_driver.o(.text.Led_Display)
    [Anonymous Symbol]                       0x08005bd8   Section        0  led_app.o(.text.Led_Init)
    [Anonymous Symbol]                       0x08005be0   Section        0  led_app.o(.text.Led_Task)
    [Anonymous Symbol]                       0x08005bec   Section        0  dma.o(.text.MX_DMA_Init)
    [Anonymous Symbol]                       0x08005c48   Section        0  gpio.o(.text.MX_GPIO_Init)
    [Anonymous Symbol]                       0x08005d58   Section        0  i2c.o(.text.MX_I2C1_Init)
    [Anonymous Symbol]                       0x08005d9c   Section        0  i2c.o(.text.MX_I2C2_Init)
    [Anonymous Symbol]                       0x08005de0   Section        0  tim.o(.text.MX_TIM1_Init)
    [Anonymous Symbol]                       0x08005f78   Section        0  tim.o(.text.MX_TIM2_Init)
    [Anonymous Symbol]                       0x08005fe8   Section        0  tim.o(.text.MX_TIM3_Init)
    [Anonymous Symbol]                       0x08006054   Section        0  tim.o(.text.MX_TIM4_Init)
    [Anonymous Symbol]                       0x080060c0   Section        0  tim.o(.text.MX_TIM6_Init)
    [Anonymous Symbol]                       0x08006114   Section        0  usart.o(.text.MX_UART4_Init)
    [Anonymous Symbol]                       0x08006150   Section        0  usart.o(.text.MX_UART5_Init)
    [Anonymous Symbol]                       0x0800618c   Section        0  usart.o(.text.MX_USART1_UART_Init)
    [Anonymous Symbol]                       0x080061c8   Section        0  usart.o(.text.MX_USART2_UART_Init)
    [Anonymous Symbol]                       0x08006204   Section        0  stm32f4xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x08006208   Section        0  motor_driver.o(.text.Motor_Create)
    [Anonymous Symbol]                       0x080062e4   Section        0  motor_app.o(.text.Motor_Init)
    [Anonymous Symbol]                       0x08006350   Section        0  stm32f4xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x08006354   Section        0  oled.o(.text.OLED_Clear)
    [Anonymous Symbol]                       0x08006570   Section        0  oled.o(.text.OLED_Init)
    [Anonymous Symbol]                       0x08006834   Section        0  oled.o(.text.OLED_ShowChar)
    [Anonymous Symbol]                       0x08006bd8   Section        0  oled.o(.text.OLED_ShowStr)
    [Anonymous Symbol]                       0x08006c18   Section        0  oled_app.o(.text.Oled_Init)
    [Anonymous Symbol]                       0x08006c28   Section        0  oled_app.o(.text.Oled_Task)
    [Anonymous Symbol]                       0x08006c98   Section        0  pid_app.o(.text.PID_Init)
    [Anonymous Symbol]                       0x08006d58   Section        0  stm32f4xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x08006d5c   Section        0  stm32f4xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x08006d60   Section        0  scheduler.o(.text.Scheduler_Init)
    [Anonymous Symbol]                       0x08006d74   Section        0  scheduler.o(.text.Scheduler_Run)
    [Anonymous Symbol]                       0x08006dc0   Section        0  stm32f4xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x08006dc4   Section        0  main.o(.text.SystemClock_Config)
    [Anonymous Symbol]                       0x08006e6c   Section        0  system_stm32f4xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x08006e80   Section        0  scheduler_task.o(.text.System_Init)
    [Anonymous Symbol]                       0x08006edc   Section        0  stm32f4xx_it.o(.text.TIM2_IRQHandler)
    [Anonymous Symbol]                       0x08006ee8   Section        0  stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    [Anonymous Symbol]                       0x08007024   Section        0  stm32f4xx_it.o(.text.UART5_IRQHandler)
    UART_DMAAbortOnError                     0x08007031   Thumb Code    10  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    [Anonymous Symbol]                       0x08007030   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    UART_DMAError                            0x0800703d   Thumb Code   380  stm32f4xx_hal_uart.o(.text.UART_DMAError)
    [Anonymous Symbol]                       0x0800703c   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAError)
    UART_DMAReceiveCplt                      0x080071b9   Thumb Code   350  stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    [Anonymous Symbol]                       0x080071b8   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    UART_DMARxHalfCplt                       0x08007319   Thumb Code    24  stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    [Anonymous Symbol]                       0x08007318   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    UART_Receive_IT                          0x08007331   Thumb Code   254  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    [Anonymous Symbol]                       0x08007330   Section        0  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    UART_SetConfig                           0x08007431   Thumb Code   230  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08007430   Section        0  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08007518   Section        0  stm32f4xx_it.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x08007524   Section        0  stm32f4xx_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x08007528   Section        0  btn_app.o(.text.app_btn_init)
    [Anonymous Symbol]                       0x08007558   Section        0  btn_app.o(.text.btn_task)
    [Anonymous Symbol]                       0x08007568   Section        0  ebtn.o(.text.ebtn_init)
    [Anonymous Symbol]                       0x080075b4   Section        0  ebtn.o(.text.ebtn_process)
    [Anonymous Symbol]                       0x08007650   Section        0  ebtn.o(.text.ebtn_process_with_curr_state)
    [Anonymous Symbol]                       0x080078fc   Section        0  jy901s_app.o(.text.jy901s_init)
    [Anonymous Symbol]                       0x08007930   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x08007978   Section        0  uart_app.o(.text.my_printf)
    [Anonymous Symbol]                       0x080079b8   Section        0  oled_driver.o(.text.oled_printf)
    [Anonymous Symbol]                       0x080079f4   Section        0  pid.o(.text.pid_init)
    [Anonymous Symbol]                       0x08007a10   Section        0  pid.o(.text.pid_set_target)
    [Anonymous Symbol]                       0x08007a18   Section        0  btn_app.o(.text.prv_btn_event)
    [Anonymous Symbol]                       0x08007a3c   Section        0  btn_app.o(.text.prv_btn_get_state)
    prv_process_btn                          0x08007a71   Thumb Code   410  ebtn.o(.text.prv_process_btn)
    [Anonymous Symbol]                       0x08007a70   Section        0  ebtn.o(.text.prv_process_btn)
    [Anonymous Symbol]                       0x08007c0c   Section        0  ringbuffer.o(.text.rt_ringbuffer_data_len)
    [Anonymous Symbol]                       0x08007c50   Section        0  ringbuffer.o(.text.rt_ringbuffer_get)
    [Anonymous Symbol]                       0x08007d08   Section        0  ringbuffer.o(.text.rt_ringbuffer_init)
    [Anonymous Symbol]                       0x08007d18   Section        0  ringbuffer.o(.text.rt_ringbuffer_put)
    [Anonymous Symbol]                       0x08007dcc   Section        0  uart_app.o(.text.uart_init)
    [Anonymous Symbol]                       0x08007e0c   Section        0  uart_app.o(.text.uart_task)
    CL$$btod_d2e                             0x08007e60   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08007e9e   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08007ee4   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08007f44   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x0800827c   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08008358   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08008382   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x080083ac   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x080085f0   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x08008620   Section        0  __printf_wp.o(i._is_digit)
    locale$$code                             0x08008630   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x0800865c   Section       44  lc_ctype_c.o(locale$$code)
    $v0                                      0x08008688   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$d2f                                0x08008688   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x080086ec   Number         0  ddiv.o(x$fpl$ddiv)
    x$fpl$ddiv                               0x080086ec   Section      696  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x080086f3   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    $v0                                      0x080089a4   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dnaninf                            0x080089a4   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08008a40   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$dretinf                            0x08008a40   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08008a4c   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$f2d                                0x08008a4c   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08008aa2   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fnaninf                            0x08008aa2   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08008b2e   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fpinit                             0x08008b2e   Section       26  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08008b48   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$fretinf                            0x08008b48   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x08008b52   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf1                            0x08008b52   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08008b56   Number         0  printf2.o(x$fpl$printf2)
    x$fpl$printf2                            0x08008b56   Section        4  printf2.o(x$fpl$printf2)
    x$fpl$usenofp                            0x08008b5a   Section        0  usenofp.o(x$fpl$usenofp)
    initial_mbstate                          0x08008b5c   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08008b5c   Section        8  _printf_wctomb.o(.constdata)
    uc_hextab                                0x08008b64   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x08008b64   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x08008b78   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    maptable                                 0x08008b8c   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08008b8c   Section       17  __printf_flags_ss_wp.o(.constdata)
    lc_hextab                                0x08008b9d   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x08008b9d   Section       38  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x08008bb0   Data          19  _printf_fp_hex.o(.constdata)
    tenpwrs_x                                0x08008bc4   Data          60  bigflt0.o(.constdata)
    .constdata                               0x08008bc4   Section      148  bigflt0.o(.constdata)
    tenpwrs_i                                0x08008c00   Data          64  bigflt0.o(.constdata)
    F8X16                                    0x08008e98   Data        1520  oled.o(.rodata.F8X16)
    [Anonymous Symbol]                       0x08008e98   Section        0  oled.o(.rodata.F8X16)
    DMA_CalcBaseAndBitshift.flagBitshiftOffset 0x08009488   Data           8  stm32f4xx_hal_dma.o(.rodata.cst8)
    [Anonymous Symbol]                       0x08009488   Section        0  stm32f4xx_hal_dma.o(.rodata.cst8)
    defaul_ebtn_param                        0x08009490   Data          14  btn_app.o(.rodata.defaul_ebtn_param)
    [Anonymous Symbol]                       0x08009490   Section        0  btn_app.o(.rodata.defaul_ebtn_param)
    locale$$data                             0x080094c0   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080094c4   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080094cc   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x080094d8   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x080094da   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x080094db   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x080094dc   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x080094dc   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x080094e0   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x080094e8   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x080095ec   Data           0  lc_ctype_c.o(locale$$data)
    .L_MergedGlobals                         0x20000000   Data           8  stm32f4xx_hal.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000000   Section        0  stm32f4xx_hal.o(.data..L_MergedGlobals)
    .L_MergedGlobals                         0x20000008   Data          84  pid_app.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000008   Section        0  pid_app.o(.data..L_MergedGlobals)
    Led_Display.temp_old                     0x2000005c   Data           1  led_driver.o(.data.Led_Display.temp_old)
    [Anonymous Symbol]                       0x2000005c   Section        0  led_driver.o(.data.Led_Display.temp_old)
    btns                                     0x20000064   Data         168  btn_app.o(.data.btns)
    [Anonymous Symbol]                       0x20000064   Section        0  btn_app.o(.data.btns)
    scheduler_task                           0x20000124   Data          60  scheduler.o(.data.scheduler_task)
    [Anonymous Symbol]                       0x20000124   Section        0  scheduler.o(.data.scheduler_task)
    .bss                                     0x20000168   Section       96  libspace.o(.bss)
    .L_MergedGlobals                         0x200001c8   Data          76  uart_app.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x200001c8   Section        0  uart_app.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000214   Data          96  motor_app.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000214   Section        0  motor_app.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000274   Data          32  encoder_app.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000274   Section        0  encoder_app.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000294   Data           8  gray_app.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000294   Section        0  gray_app.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x2000029c   Data          40  jy901s_app.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x2000029c   Section        0  jy901s_app.o(.bss..L_MergedGlobals)
    jy901s_task.last_watchdog_check          0x200002a0   Data           4  jy901s_app.o(.bss..L_MergedGlobals)
    .L_MergedGlobals.1                       0x200002c4   Data         124  pid_app.o(.bss..L_MergedGlobals.1)
    [Anonymous Symbol]                       0x200002c4   Section        0  pid_app.o(.bss..L_MergedGlobals.1)
    .L_MergedGlobals.2                       0x20000340   Data         120  pid_app.o(.bss..L_MergedGlobals.2)
    [Anonymous Symbol]                       0x20000340   Section        0  pid_app.o(.bss..L_MergedGlobals.2)
    ebtn_default                             0x200003b8   Data          52  ebtn.o(.bss.ebtn_default)
    [Anonymous Symbol]                       0x200003b8   Section        0  ebtn.o(.bss.ebtn_default)
    Heap_Mem                                 0x200008e0   Data         512  startup_stm32f407xx.o(HEAP)
    HEAP                                     0x200008e0   Section      512  startup_stm32f407xx.o(HEAP)
    Stack_Mem                                0x20000ae0   Data        1024  startup_stm32f407xx.o(STACK)
    STACK                                    0x20000ae0   Section     1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20000ee0   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    84  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x0800019b   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001ed   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001ed   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_null                       0x08000249   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x0800024d   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x08000269   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x08000269   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x0800026f   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000275   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800027b   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000281   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x08000287   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x0800028d   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x08000297   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x0800029d   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x080002a3   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x080002a9   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x080002af   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x080002b5   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x080002bb   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x080002c1   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x080002c7   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x080002cd   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x080002d3   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x080002dd   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080002e3   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080002e9   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080002ef   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080002f5   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080002f9   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080002fb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x080002ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_common                  0x080002ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_preinit_1                  0x080002ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x080002ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x080002ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_user_alloc_1               0x080002ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_lc_collate_1               0x08000305   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_2                 0x08000305   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000014)
    __rt_lib_init_lc_ctype_1                 0x08000311   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x08000311   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_2               0x08000311   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000018)
    __rt_lib_init_alloca_1                   0x0800031b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x0800031b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x0800031b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x0800031b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x0800031b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x0800031b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_trap_1                  0x0800031b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x0800031b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_lc_numeric_1               0x0800031b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x0800031b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_return                     0x0800031b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x0800031b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x0800031b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_shutdown                        0x0800031d   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800031f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800031f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0800031f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0800031f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0800031f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0800031f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0800031f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000321   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000321   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000321   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000327   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000327   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800032b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800032b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000333   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000335   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000335   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000339   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000341   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x0800035b   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x0800035d   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x08000381   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000381   Thumb Code   240  lludivv7m.o(.text)
    vsnprintf                                0x08000471   Thumb Code    56  vsnprintf.o(.text)
    memcmp                                   0x080004ad   Thumb Code    88  memcmp.o(.text)
    __aeabi_memcpy                           0x08000505   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000505   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x0800056b   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memclr                           0x0800058f   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x0800058f   Thumb Code     0  rt_memclr.o(.text)
    _memset                                  0x08000593   Thumb Code    64  rt_memclr.o(.text)
    __aeabi_memclr4                          0x080005d3   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080005d3   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080005d3   Thumb Code     0  rt_memclr_w.o(.text)
    _memset_w                                0x080005d7   Thumb Code    74  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000621   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000623   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000625   Thumb Code     2  heapauxi.o(.text)
    _printf_pre_padding                      0x08000627   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000653   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08000675   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08000687   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x08000699   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080006ed   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08000765   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x08000797   Thumb Code    32  _printf_char_common.o(.text)
    _snputc                                  0x080007bd   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x080007cd   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08000889   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08000905   Thumb Code    68  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000949   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000961   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000975   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x080009cb   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x080009e7   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x080009f3   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x08000a09   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __aeabi_memcpy4                          0x08000b91   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000b91   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000b91   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000bd9   Thumb Code     0  rt_memcpy_w.o(.text)
    _ll_udiv10                               0x08000bf5   Thumb Code   138  lludiv10.o(.text)
    _printf_int_common                       0x08000c7f   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_fp_dec_real                      0x08000ee1   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x0800114d   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x08001449   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x0800145d   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x0800146d   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x08001475   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001489   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001499   Thumb Code     8  _printf_wchar.o(.text)
    _c16rtomb                                0x080014a1   Thumb Code    72  _c16rtomb.o(.text)
    _wcrtomb                                 0x080014a1   Thumb Code     0  _c16rtomb.o(.text)
    __user_setup_stackheap                   0x080014e9   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08001535   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x08001545   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x0800154d   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x080015cd   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x080016b1   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x080016c5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080016c5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080016c5   Thumb Code     0  libspace.o(.text)
    strcmp                                   0x080016d1   Thumb Code   124  strcmpv7em.o(.text)
    _sys_exit                                0x0800174d   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08001759   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001759   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x0800175b   Thumb Code     0  indicate_semi.o(.text)
    BusFault_Handler                         0x0800175d   Thumb Code     2  stm32f4xx_it.o(.text.BusFault_Handler)
    DMA1_Stream0_IRQHandler                  0x08001761   Thumb Code    12  stm32f4xx_it.o(.text.DMA1_Stream0_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x0800176d   Thumb Code    12  stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x08001779   Thumb Code     2  stm32f4xx_it.o(.text.DebugMon_Handler)
    Encoder_Driver_Init                      0x0800177d   Thumb Code    32  encoder_driver.o(.text.Encoder_Driver_Init)
    Encoder_Driver_Update                    0x080017a1   Thumb Code    88  encoder_driver.o(.text.Encoder_Driver_Update)
    Encoder_Init                             0x08001809   Thumb Code    48  encoder_app.o(.text.Encoder_Init)
    Encoder_Task                             0x08001839   Thumb Code    76  encoder_app.o(.text.Encoder_Task)
    Error_Handler                            0x08001891   Thumb Code     6  main.o(.text.Error_Handler)
    Gray_Init                                0x08001899   Thumb Code     2  gray_app.o(.text.Gray_Init)
    HAL_DMA_Abort                            0x0800189d   Thumb Code   142  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x0800192d   Thumb Code    36  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    HAL_DMA_GetState                         0x08001951   Thumb Code     6  stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState)
    HAL_DMA_IRQHandler                       0x08001959   Thumb Code   452  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08001b1d   Thumb Code   354  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08001c81   Thumb Code   162  stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    HAL_Delay                                0x08001d25   Thumb Code    40  stm32f4xx_hal.o(.text.HAL_Delay)
    HAL_GPIO_Init                            0x08001d4d   Thumb Code   414  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08001eed   Thumb Code    10  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08001ef9   Thumb Code    10  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001f05   Thumb Code    12  stm32f4xx_hal.o(.text.HAL_GetTick)
    HAL_I2C_AbortCpltCallback                0x08001f11   Thumb Code     2  stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback)
    HAL_I2C_AddrCallback                     0x08001f15   Thumb Code     2  stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback)
    HAL_I2C_EV_IRQHandler                    0x08001f19   Thumb Code  1532  stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler)
    HAL_I2C_ErrorCallback                    0x08002515   Thumb Code     2  stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback)
    HAL_I2C_Init                             0x08002519   Thumb Code   356  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init)
    HAL_I2C_ListenCpltCallback               0x0800267d   Thumb Code     2  stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback)
    HAL_I2C_MasterRxCpltCallback             0x08002681   Thumb Code     2  stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback)
    HAL_I2C_MasterTxCpltCallback             0x08002685   Thumb Code     2  stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback)
    HAL_I2C_MemRxCpltCallback                0x08002689   Thumb Code     2  stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback)
    HAL_I2C_MemTxCpltCallback                0x0800268d   Thumb Code     2  stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback)
    HAL_I2C_Mem_Write                        0x08002691   Thumb Code   348  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x080027ed   Thumb Code   228  i2c.o(.text.HAL_I2C_MspInit)
    HAL_I2C_SlaveRxCpltCallback              0x080028d1   Thumb Code     2  stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback)
    HAL_I2C_SlaveTxCpltCallback              0x080028d5   Thumb Code     2  stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback)
    HAL_IncTick                              0x080028d9   Thumb Code    26  stm32f4xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x080028f5   Thumb Code    54  stm32f4xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x0800292d   Thumb Code    72  stm32f4xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08002975   Thumb Code    56  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x080029ad   Thumb Code    34  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080029d1   Thumb Code    86  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002a29   Thumb Code    32  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08002a49   Thumb Code   356  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08002bad   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08002bd5   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08002bfd   Thumb Code   108  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08002c69   Thumb Code   940  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08003015   Thumb Code    44  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x08003041   Thumb Code     2  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08003045   Thumb Code     2  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x08003049   Thumb Code    76  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08003095   Thumb Code   186  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08003151   Thumb Code    90  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x080031ad   Thumb Code   156  tim.o(.text.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08003249   Thumb Code   170  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x080032f5   Thumb Code   416  stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x08003495   Thumb Code   178  stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08003549   Thumb Code   190  tim.o(.text.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x08003609   Thumb Code   188  stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start)
    HAL_TIM_IC_CaptureCallback               0x080036c5   Thumb Code     2  stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x080036c9   Thumb Code   334  stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x08003819   Thumb Code     2  stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x0800381d   Thumb Code   544  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08003a3d   Thumb Code    90  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08003a99   Thumb Code     2  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08003a9d   Thumb Code     2  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08003aa1   Thumb Code   270  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x08003bb1   Thumb Code     2  btn_app.o(.text.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08003bb5   Thumb Code     2  stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08003bb9   Thumb Code   494  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08003da9   Thumb Code    92  uart_app.o(.text.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08003e05   Thumb Code   540  stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x08004021   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08004025   Thumb Code  1428  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080045b9   Thumb Code    96  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    HAL_UART_MspInit                         0x08004619   Thumb Code   638  usart.o(.text.HAL_UART_MspInit)
    HAL_UART_Receive_DMA                     0x08004899   Thumb Code   384  stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA)
    HAL_UART_RxCpltCallback                  0x08004a19   Thumb Code    66  jy901s_app.o(.text.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08004a5d   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08004a61   Thumb Code   402  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08004bf5   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08004bf9   Thumb Code     2  stm32f4xx_it.o(.text.HardFault_Handler)
    I2C1_EV_IRQHandler                       0x08004bfd   Thumb Code    12  stm32f4xx_it.o(.text.I2C1_EV_IRQHandler)
    I2C2_EV_IRQHandler                       0x08004c09   Thumb Code    12  stm32f4xx_it.o(.text.I2C2_EV_IRQHandler)
    JY901S_Create                            0x080057e1   Thumb Code   112  jy901s_driver.o(.text.JY901S_Create)
    JY901S_ProcessBuffer                     0x08005851   Thumb Code   740  jy901s_driver.o(.text.JY901S_ProcessBuffer)
    Led_Display                              0x08005b49   Thumb Code   142  led_driver.o(.text.Led_Display)
    Led_Init                                 0x08005bd9   Thumb Code     6  led_app.o(.text.Led_Init)
    Led_Task                                 0x08005be1   Thumb Code    12  led_app.o(.text.Led_Task)
    MX_DMA_Init                              0x08005bed   Thumb Code    92  dma.o(.text.MX_DMA_Init)
    MX_GPIO_Init                             0x08005c49   Thumb Code   272  gpio.o(.text.MX_GPIO_Init)
    MX_I2C1_Init                             0x08005d59   Thumb Code    66  i2c.o(.text.MX_I2C1_Init)
    MX_I2C2_Init                             0x08005d9d   Thumb Code    68  i2c.o(.text.MX_I2C2_Init)
    MX_TIM1_Init                             0x08005de1   Thumb Code   406  tim.o(.text.MX_TIM1_Init)
    MX_TIM2_Init                             0x08005f79   Thumb Code   112  tim.o(.text.MX_TIM2_Init)
    MX_TIM3_Init                             0x08005fe9   Thumb Code   106  tim.o(.text.MX_TIM3_Init)
    MX_TIM4_Init                             0x08006055   Thumb Code   106  tim.o(.text.MX_TIM4_Init)
    MX_TIM6_Init                             0x080060c1   Thumb Code    82  tim.o(.text.MX_TIM6_Init)
    MX_UART4_Init                            0x08006115   Thumb Code    60  usart.o(.text.MX_UART4_Init)
    MX_UART5_Init                            0x08006151   Thumb Code    60  usart.o(.text.MX_UART5_Init)
    MX_USART1_UART_Init                      0x0800618d   Thumb Code    60  usart.o(.text.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x080061c9   Thumb Code    60  usart.o(.text.MX_USART2_UART_Init)
    MemManage_Handler                        0x08006205   Thumb Code     2  stm32f4xx_it.o(.text.MemManage_Handler)
    Motor_Create                             0x08006209   Thumb Code   218  motor_driver.o(.text.Motor_Create)
    Motor_Init                               0x080062e5   Thumb Code   106  motor_app.o(.text.Motor_Init)
    NMI_Handler                              0x08006351   Thumb Code     2  stm32f4xx_it.o(.text.NMI_Handler)
    OLED_Clear                               0x08006355   Thumb Code   540  oled.o(.text.OLED_Clear)
    OLED_Init                                0x08006571   Thumb Code   706  oled.o(.text.OLED_Init)
    OLED_ShowChar                            0x08006835   Thumb Code   932  oled.o(.text.OLED_ShowChar)
    OLED_ShowStr                             0x08006bd9   Thumb Code    64  oled.o(.text.OLED_ShowStr)
    Oled_Init                                0x08006c19   Thumb Code    14  oled_app.o(.text.Oled_Init)
    Oled_Task                                0x08006c29   Thumb Code    80  oled_app.o(.text.Oled_Task)
    PID_Init                                 0x08006c99   Thumb Code   188  pid_app.o(.text.PID_Init)
    PendSV_Handler                           0x08006d59   Thumb Code     2  stm32f4xx_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x08006d5d   Thumb Code     2  stm32f4xx_it.o(.text.SVC_Handler)
    Scheduler_Init                           0x08006d61   Thumb Code    20  scheduler.o(.text.Scheduler_Init)
    Scheduler_Run                            0x08006d75   Thumb Code    74  scheduler.o(.text.Scheduler_Run)
    SysTick_Handler                          0x08006dc1   Thumb Code     4  stm32f4xx_it.o(.text.SysTick_Handler)
    SystemClock_Config                       0x08006dc5   Thumb Code   168  main.o(.text.SystemClock_Config)
    SystemInit                               0x08006e6d   Thumb Code    18  system_stm32f4xx.o(.text.SystemInit)
    System_Init                              0x08006e81   Thumb Code    68  scheduler_task.o(.text.System_Init)
    TIM2_IRQHandler                          0x08006edd   Thumb Code    12  stm32f4xx_it.o(.text.TIM2_IRQHandler)
    TIM_Base_SetConfig                       0x08006ee9   Thumb Code   314  stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    UART5_IRQHandler                         0x08007025   Thumb Code    12  stm32f4xx_it.o(.text.UART5_IRQHandler)
    USART1_IRQHandler                        0x08007519   Thumb Code    12  stm32f4xx_it.o(.text.USART1_IRQHandler)
    UsageFault_Handler                       0x08007525   Thumb Code     2  stm32f4xx_it.o(.text.UsageFault_Handler)
    app_btn_init                             0x08007529   Thumb Code    46  btn_app.o(.text.app_btn_init)
    btn_task                                 0x08007559   Thumb Code    14  btn_app.o(.text.btn_task)
    ebtn_init                                0x08007569   Thumb Code    76  ebtn.o(.text.ebtn_init)
    ebtn_process                             0x080075b5   Thumb Code   154  ebtn.o(.text.ebtn_process)
    ebtn_process_with_curr_state             0x08007651   Thumb Code   684  ebtn.o(.text.ebtn_process_with_curr_state)
    jy901s_init                              0x080078fd   Thumb Code    52  jy901s_app.o(.text.jy901s_init)
    main                                     0x08007931   Thumb Code    70  main.o(.text.main)
    my_printf                                0x08007979   Thumb Code    62  uart_app.o(.text.my_printf)
    oled_printf                              0x080079b9   Thumb Code    58  oled_driver.o(.text.oled_printf)
    pid_init                                 0x080079f5   Thumb Code    26  pid.o(.text.pid_init)
    pid_set_target                           0x08007a11   Thumb Code     6  pid.o(.text.pid_set_target)
    prv_btn_event                            0x08007a19   Thumb Code    36  btn_app.o(.text.prv_btn_event)
    prv_btn_get_state                        0x08007a3d   Thumb Code    44  btn_app.o(.text.prv_btn_get_state)
    rt_ringbuffer_data_len                   0x08007c0d   Thumb Code    68  ringbuffer.o(.text.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x08007c51   Thumb Code   184  ringbuffer.o(.text.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x08007d09   Thumb Code    14  ringbuffer.o(.text.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x08007d19   Thumb Code   178  ringbuffer.o(.text.rt_ringbuffer_put)
    uart_init                                0x08007dcd   Thumb Code    64  uart_app.o(.text.uart_init)
    uart_task                                0x08007e0d   Thumb Code    72  uart_app.o(.text.uart_task)
    _btod_d2e                                0x08007e61   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08007e9f   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08007ee5   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08007f45   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x0800827d   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08008359   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08008383   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x080083ad   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x080085f1   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x08008621   Thumb Code    14  __printf_wp.o(i._is_digit)
    _get_lc_numeric                          0x08008631   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x0800865d   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_d2f                              0x08008689   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08008689   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_ddiv                             0x080086ed   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x080086ed   Thumb Code   560  ddiv.o(x$fpl$ddiv)
    __fpl_dnaninf                            0x080089a5   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08008a41   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x08008a4d   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08008a4d   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08008aa3   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x08008b2f   Thumb Code    26  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08008b47   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08008b47   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x08008b49   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x08008b53   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08008b57   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x08008b5a   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x08008c58   Data          16  system_stm32f4xx.o(.rodata.AHBPrescTable)
    APBPrescTable                            0x08008c68   Data           8  system_stm32f4xx.o(.rodata.APBPrescTable)
    F6X8                                     0x08008c70   Data         552  oled.o(.rodata.F6X8)
    Region$$Table$$Base                      0x080094a0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080094c0   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x080094e9   Data           0  lc_ctype_c.o(locale$$data)
    uwTickFreq                               0x20000000   Data           1  stm32f4xx_hal.o(.data..L_MergedGlobals)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data..L_MergedGlobals)
    basic_speed                              0x20000008   Data           4  pid_app.o(.data..L_MergedGlobals)
    pid_params_left                          0x2000000c   Data          20  pid_app.o(.data..L_MergedGlobals)
    pid_params_right                         0x20000020   Data          20  pid_app.o(.data..L_MergedGlobals)
    pid_params_line                          0x20000034   Data          20  pid_app.o(.data..L_MergedGlobals)
    pid_params_angle                         0x20000048   Data          20  pid_app.o(.data..L_MergedGlobals)
    SystemCoreClock                          0x20000060   Data           4  system_stm32f4xx.o(.data.SystemCoreClock)
    initcmd1                                 0x2000010c   Data          22  oled.o(.data.initcmd1)
    ucLed                                    0x20000160   Data           4  led_app.o(.data.ucLed)
    __libspace_start                         0x20000168   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200001c8   Data           0  libspace.o(.bss)
    uart_ringbuffer                          0x200001c8   Data          12  uart_app.o(.bss..L_MergedGlobals)
    uart_rx_dma_buffer                       0x200001d4   Data          64  uart_app.o(.bss..L_MergedGlobals)
    left_motor                               0x20000214   Data          48  motor_app.o(.bss..L_MergedGlobals)
    right_motor                              0x20000244   Data          48  motor_app.o(.bss..L_MergedGlobals)
    left_encoder                             0x20000274   Data          16  encoder_app.o(.bss..L_MergedGlobals)
    right_encoder                            0x20000284   Data          16  encoder_app.o(.bss..L_MergedGlobals)
    Digtal                                   0x20000294   Data           1  gray_app.o(.bss..L_MergedGlobals)
    g_line_position_error                    0x20000298   Data           4  gray_app.o(.bss..L_MergedGlobals)
    uart5_flag                               0x2000029c   Data           1  jy901s_app.o(.bss..L_MergedGlobals)
    uart5_rx_buffer                          0x200002a4   Data          32  jy901s_app.o(.bss..L_MergedGlobals)
    pid_running                              0x200002c4   Data           1  pid_app.o(.bss..L_MergedGlobals.1)
    pid_control_mode                         0x200002c5   Data           1  pid_app.o(.bss..L_MergedGlobals.1)
    pid_speed_left                           0x200002c8   Data          60  pid_app.o(.bss..L_MergedGlobals.1)
    pid_speed_right                          0x20000304   Data          60  pid_app.o(.bss..L_MergedGlobals.1)
    pid_line                                 0x20000340   Data          60  pid_app.o(.bss..L_MergedGlobals.2)
    pid_angle                                0x2000037c   Data          60  pid_app.o(.bss..L_MergedGlobals.2)
    hdma_uart5_rx                            0x200003ec   Data          96  usart.o(.bss.hdma_uart5_rx)
    hdma_usart1_rx                           0x2000044c   Data          96  usart.o(.bss.hdma_usart1_rx)
    hi2c1                                    0x200004ac   Data          84  i2c.o(.bss.hi2c1)
    hi2c2                                    0x20000500   Data          84  i2c.o(.bss.hi2c2)
    htim1                                    0x20000554   Data          72  tim.o(.bss.htim1)
    htim2                                    0x2000059c   Data          72  tim.o(.bss.htim2)
    htim3                                    0x200005e4   Data          72  tim.o(.bss.htim3)
    htim4                                    0x2000062c   Data          72  tim.o(.bss.htim4)
    htim6                                    0x20000674   Data          72  tim.o(.bss.htim6)
    huart1                                   0x200006bc   Data          72  usart.o(.bss.huart1)
    huart2                                   0x20000704   Data          72  usart.o(.bss.huart2)
    huart4                                   0x2000074c   Data          72  usart.o(.bss.huart4)
    huart5                                   0x20000794   Data          72  usart.o(.bss.huart5)
    jy901s                                   0x200007dc   Data         124  jy901s_app.o(.bss.jy901s)
    task_num                                 0x20000858   Data           1  scheduler.o(.bss.task_num)
    uart_out_buffer                          0x2000085c   Data          64  uart_app.o(.bss.uart_out_buffer)
    uart_ringbuffer_pool                     0x2000089c   Data          64  uart_app.o(.bss.uart_ringbuffer_pool)
    uwTick                                   0x200008dc   Data           4  stm32f4xx_hal.o(.bss.uwTick)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00009758, Max: 0x00080000, ABSOLUTE, COMPRESSED[0x00009670])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000095ec, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1658  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x0000005c   Code   RO         2031    !!!scatter          c_w.l(__scatter.o)
    0x080001ec   0x080001ec   0x0000005a   Code   RO         2029    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x08000246   0x08000246   0x00000002   PAD
    0x08000248   0x08000248   0x00000002   Code   RO         2032    !!handler_null      c_w.l(__scatter.o)
    0x0800024a   0x0800024a   0x00000002   PAD
    0x0800024c   0x0800024c   0x0000001c   Code   RO         2035    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000268   0x08000268   0x00000000   Code   RO         1758    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000268   0x08000268   0x00000006   Code   RO         1747    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x0800026e   0x0800026e   0x00000006   Code   RO         1749    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000274   0x08000274   0x00000006   Code   RO         1754    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800027a   0x0800027a   0x00000006   Code   RO         1755    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000280   0x08000280   0x00000006   Code   RO         1756    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x08000286   0x08000286   0x00000006   Code   RO         1757    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x0800028c   0x0800028c   0x0000000a   Code   RO         1762    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x08000296   0x08000296   0x00000006   Code   RO         1751    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x0800029c   0x0800029c   0x00000006   Code   RO         1752    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x080002a2   0x080002a2   0x00000006   Code   RO         1753    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x080002a8   0x080002a8   0x00000006   Code   RO         1750    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x080002ae   0x080002ae   0x00000006   Code   RO         1748    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x080002b4   0x080002b4   0x00000006   Code   RO         1759    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x080002ba   0x080002ba   0x00000006   Code   RO         1760    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x080002c0   0x080002c0   0x00000006   Code   RO         1761    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x080002c6   0x080002c6   0x00000006   Code   RO         1766    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x080002cc   0x080002cc   0x00000006   Code   RO         1767    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x080002d2   0x080002d2   0x0000000a   Code   RO         1763    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x080002dc   0x080002dc   0x00000006   Code   RO         1745    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080002e2   0x080002e2   0x00000006   Code   RO         1746    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080002e8   0x080002e8   0x00000006   Code   RO         1764    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080002ee   0x080002ee   0x00000006   Code   RO         1765    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080002f4   0x080002f4   0x00000004   Code   RO         1823    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080002f8   0x080002f8   0x00000002   Code   RO         1878    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080002fa   0x080002fa   0x00000004   Code   RO         1901    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080002fe   0x080002fe   0x00000000   Code   RO         1904    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080002fe   0x080002fe   0x00000000   Code   RO         1906    .ARM.Collect$$libinit$$00000006  c_w.l(libinit2.o)
    0x080002fe   0x080002fe   0x00000000   Code   RO         1909    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080002fe   0x080002fe   0x00000000   Code   RO         1911    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080002fe   0x080002fe   0x00000000   Code   RO         1913    .ARM.Collect$$libinit$$00000010  c_w.l(libinit2.o)
    0x080002fe   0x080002fe   0x00000006   Code   RO         1914    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000304   0x08000304   0x00000000   Code   RO         1916    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000304   0x08000304   0x0000000c   Code   RO         1917    .ARM.Collect$$libinit$$00000014  c_w.l(libinit2.o)
    0x08000310   0x08000310   0x00000000   Code   RO         1918    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000310   0x08000310   0x00000000   Code   RO         1920    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000310   0x08000310   0x0000000a   Code   RO         1921    .ARM.Collect$$libinit$$00000018  c_w.l(libinit2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         1922    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         1924    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         1926    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         1928    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         1930    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         1932    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         1934    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         1936    .ARM.Collect$$libinit$$00000027  c_w.l(libinit2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         1940    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         1942    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         1944    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         1946    .ARM.Collect$$libinit$$00000034  c_w.l(libinit2.o)
    0x0800031a   0x0800031a   0x00000002   Code   RO         1947    .ARM.Collect$$libinit$$00000035  c_w.l(libinit2.o)
    0x0800031c   0x0800031c   0x00000002   Code   RO         1984    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800031e   0x0800031e   0x00000000   Code   RO         2012    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800031e   0x0800031e   0x00000000   Code   RO         2014    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800031e   0x0800031e   0x00000000   Code   RO         2017    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x0800031e   0x0800031e   0x00000000   Code   RO         2020    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x0800031e   0x0800031e   0x00000000   Code   RO         2022    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800031e   0x0800031e   0x00000000   Code   RO         2025    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x0800031e   0x0800031e   0x00000002   Code   RO         2026    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000320   0x08000320   0x00000000   Code   RO         1672    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000320   0x08000320   0x00000000   Code   RO         1790    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000320   0x08000320   0x00000006   Code   RO         1802    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000326   0x08000326   0x00000000   Code   RO         1792    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000326   0x08000326   0x00000004   Code   RO         1793    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800032a   0x0800032a   0x00000000   Code   RO         1795    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800032a   0x0800032a   0x00000008   Code   RO         1796    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000332   0x08000332   0x00000002   Code   RO         1886    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000334   0x08000334   0x00000000   Code   RO         1953    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000334   0x08000334   0x00000004   Code   RO         1954    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000338   0x08000338   0x00000006   Code   RO         1955    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800033e   0x0800033e   0x00000002   PAD
    0x08000340   0x08000340   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x08000380   0x08000380   0x000000f0   Code   RO         1644    .text               c_w.l(lludivv7m.o)
    0x08000470   0x08000470   0x0000003c   Code   RO         1646    .text               c_w.l(vsnprintf.o)
    0x080004ac   0x080004ac   0x00000058   Code   RO         1648    .text               c_w.l(memcmp.o)
    0x08000504   0x08000504   0x0000008a   Code   RO         1650    .text               c_w.l(rt_memcpy_v6.o)
    0x0800058e   0x0800058e   0x00000044   Code   RO         1652    .text               c_w.l(rt_memclr.o)
    0x080005d2   0x080005d2   0x0000004e   Code   RO         1654    .text               c_w.l(rt_memclr_w.o)
    0x08000620   0x08000620   0x00000006   Code   RO         1656    .text               c_w.l(heapauxi.o)
    0x08000626   0x08000626   0x0000004e   Code   RO         1679    .text               c_w.l(_printf_pad.o)
    0x08000674   0x08000674   0x00000024   Code   RO         1681    .text               c_w.l(_printf_truncate.o)
    0x08000698   0x08000698   0x00000052   Code   RO         1683    .text               c_w.l(_printf_str.o)
    0x080006ea   0x080006ea   0x00000002   PAD
    0x080006ec   0x080006ec   0x00000078   Code   RO         1685    .text               c_w.l(_printf_dec.o)
    0x08000764   0x08000764   0x00000028   Code   RO         1687    .text               c_w.l(_printf_charcount.o)
    0x0800078c   0x0800078c   0x00000030   Code   RO         1689    .text               c_w.l(_printf_char_common.o)
    0x080007bc   0x080007bc   0x00000010   Code   RO         1691    .text               c_w.l(_snputc.o)
    0x080007cc   0x080007cc   0x000000bc   Code   RO         1693    .text               c_w.l(_printf_wctomb.o)
    0x08000888   0x08000888   0x0000007c   Code   RO         1696    .text               c_w.l(_printf_longlong_dec.o)
    0x08000904   0x08000904   0x00000070   Code   RO         1702    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000974   0x08000974   0x00000094   Code   RO         1722    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000a08   0x08000a08   0x00000188   Code   RO         1742    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000b90   0x08000b90   0x00000064   Code   RO         1768    .text               c_w.l(rt_memcpy_w.o)
    0x08000bf4   0x08000bf4   0x0000008a   Code   RO         1806    .text               c_w.l(lludiv10.o)
    0x08000c7e   0x08000c7e   0x000000b2   Code   RO         1808    .text               c_w.l(_printf_intcommon.o)
    0x08000d30   0x08000d30   0x0000041c   Code   RO         1810    .text               c_w.l(_printf_fp_dec.o)
    0x0800114c   0x0800114c   0x000002fc   Code   RO         1814    .text               c_w.l(_printf_fp_hex.o)
    0x08001448   0x08001448   0x0000002c   Code   RO         1819    .text               c_w.l(_printf_char.o)
    0x08001474   0x08001474   0x0000002c   Code   RO         1821    .text               c_w.l(_printf_wchar.o)
    0x080014a0   0x080014a0   0x00000048   Code   RO         1824    .text               c_w.l(_c16rtomb.o)
    0x080014e8   0x080014e8   0x0000004a   Code   RO         1826    .text               c_w.l(sys_stackheap_outer.o)
    0x08001532   0x08001532   0x00000002   PAD
    0x08001534   0x08001534   0x00000010   Code   RO         1828    .text               c_w.l(rt_ctype_table.o)
    0x08001544   0x08001544   0x00000008   Code   RO         1833    .text               c_w.l(rt_locale_intlibspace.o)
    0x0800154c   0x0800154c   0x00000080   Code   RO         1835    .text               c_w.l(_printf_fp_infnan.o)
    0x080015cc   0x080015cc   0x000000e4   Code   RO         1837    .text               c_w.l(bigflt0.o)
    0x080016b0   0x080016b0   0x00000012   Code   RO         1867    .text               c_w.l(exit.o)
    0x080016c2   0x080016c2   0x00000002   PAD
    0x080016c4   0x080016c4   0x00000008   Code   RO         1883    .text               c_w.l(libspace.o)
    0x080016cc   0x080016cc   0x00000004   PAD
    0x080016d0   0x080016d0   0x0000007c   Code   RO         1899    .text               c_w.l(strcmpv7em.o)
    0x0800174c   0x0800174c   0x0000000c   Code   RO         1948    .text               c_w.l(sys_exit.o)
    0x08001758   0x08001758   0x00000002   Code   RO         1973    .text               c_w.l(use_no_semi.o)
    0x0800175a   0x0800175a   0x00000000   Code   RO         1975    .text               c_w.l(indicate_semi.o)
    0x0800175a   0x0800175a   0x00000002   PAD
    0x0800175c   0x0800175c   0x00000002   Code   RO          126    .text.BusFault_Handler  stm32f4xx_it.o
    0x0800175e   0x0800175e   0x00000002   PAD
    0x08001760   0x08001760   0x0000000c   Code   RO          138    .text.DMA1_Stream0_IRQHandler  stm32f4xx_it.o
    0x0800176c   0x0800176c   0x0000000c   Code   RO          150    .text.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08001778   0x08001778   0x00000002   Code   RO          132    .text.DebugMon_Handler  stm32f4xx_it.o
    0x0800177a   0x0800177a   0x00000002   PAD
    0x0800177c   0x0800177c   0x00000020   Code   RO         1365    .text.Encoder_Driver_Init  encoder_driver.o
    0x0800179c   0x0800179c   0x00000004   PAD
    0x080017a0   0x080017a0   0x00000068   Code   RO         1367    .text.Encoder_Driver_Update  encoder_driver.o
    0x08001808   0x08001808   0x00000030   Code   RO         1549    .text.Encoder_Init  encoder_app.o
    0x08001838   0x08001838   0x00000058   Code   RO         1551    .text.Encoder_Task  encoder_app.o
    0x08001890   0x08001890   0x00000006   Code   RO           15    .text.Error_Handler  main.o
    0x08001896   0x08001896   0x00000002   PAD
    0x08001898   0x08001898   0x00000002   Code   RO         1561    .text.Gray_Init     gray_app.o
    0x0800189a   0x0800189a   0x00000002   PAD
    0x0800189c   0x0800189c   0x0000008e   Code   RO          462    .text.HAL_DMA_Abort  stm32f4xx_hal_dma.o
    0x0800192a   0x0800192a   0x00000002   PAD
    0x0800192c   0x0800192c   0x00000024   Code   RO          464    .text.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08001950   0x08001950   0x00000006   Code   RO          474    .text.HAL_DMA_GetState  stm32f4xx_hal_dma.o
    0x08001956   0x08001956   0x00000002   PAD
    0x08001958   0x08001958   0x000001c4   Code   RO          468    .text.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08001b1c   0x08001b1c   0x00000162   Code   RO          454    .text.HAL_DMA_Init  stm32f4xx_hal_dma.o
    0x08001c7e   0x08001c7e   0x00000002   PAD
    0x08001c80   0x08001c80   0x000000a2   Code   RO          460    .text.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08001d22   0x08001d22   0x00000002   PAD
    0x08001d24   0x08001d24   0x00000028   Code   RO          621    .text.HAL_Delay     stm32f4xx_hal.o
    0x08001d4c   0x08001d4c   0x0000019e   Code   RO          416    .text.HAL_GPIO_Init  stm32f4xx_hal_gpio.o
    0x08001eea   0x08001eea   0x00000002   PAD
    0x08001eec   0x08001eec   0x0000000a   Code   RO          420    .text.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x08001ef6   0x08001ef6   0x00000002   PAD
    0x08001ef8   0x08001ef8   0x0000000a   Code   RO          422    .text.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08001f02   0x08001f02   0x00000002   PAD
    0x08001f04   0x08001f04   0x0000000c   Code   RO          613    .text.HAL_GetTick   stm32f4xx_hal.o
    0x08001f10   0x08001f10   0x00000002   Code   RO          287    .text.HAL_I2C_AbortCpltCallback  stm32f4xx_hal_i2c.o
    0x08001f12   0x08001f12   0x00000002   PAD
    0x08001f14   0x08001f14   0x00000002   Code   RO          277    .text.HAL_I2C_AddrCallback  stm32f4xx_hal_i2c.o
    0x08001f16   0x08001f16   0x00000002   PAD
    0x08001f18   0x08001f18   0x000005fc   Code   RO          255    .text.HAL_I2C_EV_IRQHandler  stm32f4xx_hal_i2c.o
    0x08002514   0x08002514   0x00000002   Code   RO          285    .text.HAL_I2C_ErrorCallback  stm32f4xx_hal_i2c.o
    0x08002516   0x08002516   0x00000002   PAD
    0x08002518   0x08002518   0x00000164   Code   RO          167    .text.HAL_I2C_Init  stm32f4xx_hal_i2c.o
    0x0800267c   0x0800267c   0x00000002   Code   RO          279    .text.HAL_I2C_ListenCpltCallback  stm32f4xx_hal_i2c.o
    0x0800267e   0x0800267e   0x00000002   PAD
    0x08002680   0x08002680   0x00000002   Code   RO          271    .text.HAL_I2C_MasterRxCpltCallback  stm32f4xx_hal_i2c.o
    0x08002682   0x08002682   0x00000002   PAD
    0x08002684   0x08002684   0x00000002   Code   RO          269    .text.HAL_I2C_MasterTxCpltCallback  stm32f4xx_hal_i2c.o
    0x08002686   0x08002686   0x00000002   PAD
    0x08002688   0x08002688   0x00000002   Code   RO          283    .text.HAL_I2C_MemRxCpltCallback  stm32f4xx_hal_i2c.o
    0x0800268a   0x0800268a   0x00000002   PAD
    0x0800268c   0x0800268c   0x00000002   Code   RO          281    .text.HAL_I2C_MemTxCpltCallback  stm32f4xx_hal_i2c.o
    0x0800268e   0x0800268e   0x00000002   PAD
    0x08002690   0x08002690   0x0000015c   Code   RO          211    .text.HAL_I2C_Mem_Write  stm32f4xx_hal_i2c.o
    0x080027ec   0x080027ec   0x000000e4   Code   RO           47    .text.HAL_I2C_MspInit  i2c.o
    0x080028d0   0x080028d0   0x00000002   Code   RO          275    .text.HAL_I2C_SlaveRxCpltCallback  stm32f4xx_hal_i2c.o
    0x080028d2   0x080028d2   0x00000002   PAD
    0x080028d4   0x080028d4   0x00000002   Code   RO          273    .text.HAL_I2C_SlaveTxCpltCallback  stm32f4xx_hal_i2c.o
    0x080028d6   0x080028d6   0x00000002   PAD
    0x080028d8   0x080028d8   0x0000001a   Code   RO          611    .text.HAL_IncTick   stm32f4xx_hal.o
    0x080028f2   0x080028f2   0x00000002   PAD
    0x080028f4   0x080028f4   0x00000036   Code   RO          601    .text.HAL_Init      stm32f4xx_hal.o
    0x0800292a   0x0800292a   0x00000002   PAD
    0x0800292c   0x0800292c   0x00000048   Code   RO          603    .text.HAL_InitTick  stm32f4xx_hal.o
    0x08002974   0x08002974   0x00000038   Code   RO          159    .text.HAL_MspInit   stm32f4xx_hal_msp.o
    0x080029ac   0x080029ac   0x00000022   Code   RO          553    .text.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x080029ce   0x080029ce   0x00000002   PAD
    0x080029d0   0x080029d0   0x00000056   Code   RO          551    .text.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08002a26   0x08002a26   0x00000002   PAD
    0x08002a28   0x08002a28   0x00000020   Code   RO          549    .text.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08002a48   0x08002a48   0x00000164   Code   RO          310    .text.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08002bac   0x08002bac   0x00000026   Code   RO          322    .text.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08002bd2   0x08002bd2   0x00000002   PAD
    0x08002bd4   0x08002bd4   0x00000026   Code   RO          324    .text.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08002bfa   0x08002bfa   0x00000002   PAD
    0x08002bfc   0x08002bfc   0x0000006c   Code   RO          312    .text.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08002c68   0x08002c68   0x000003ac   Code   RO          308    .text.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08003014   0x08003014   0x0000002c   Code   RO          561    .text.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08003040   0x08003040   0x00000002   Code   RO          996    .text.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x08003042   0x08003042   0x00000002   PAD
    0x08003044   0x08003044   0x00000002   Code   RO          992    .text.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x08003046   0x08003046   0x00000002   PAD
    0x08003048   0x08003048   0x0000004c   Code   RO          988    .text.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x08003094   0x08003094   0x000000ba   Code   RO          986    .text.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x0800314e   0x0800314e   0x00000002   PAD
    0x08003150   0x08003150   0x0000005a   Code   RO          691    .text.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x080031aa   0x080031aa   0x00000002   PAD
    0x080031ac   0x080031ac   0x0000009c   Code   RO           73    .text.HAL_TIM_Base_MspInit  tim.o
    0x08003248   0x08003248   0x000000aa   Code   RO          705    .text.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x080032f2   0x080032f2   0x00000002   PAD
    0x080032f4   0x080032f4   0x000001a0   Code   RO          871    .text.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08003494   0x08003494   0x000000b2   Code   RO          805    .text.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08003546   0x08003546   0x00000002   PAD
    0x08003548   0x08003548   0x000000be   Code   RO           75    .text.HAL_TIM_Encoder_MspInit  tim.o
    0x08003606   0x08003606   0x00000002   PAD
    0x08003608   0x08003608   0x000000bc   Code   RO          813    .text.HAL_TIM_Encoder_Start  stm32f4xx_hal_tim.o
    0x080036c4   0x080036c4   0x00000002   Code   RO          827    .text.HAL_TIM_IC_CaptureCallback  stm32f4xx_hal_tim.o
    0x080036c6   0x080036c6   0x00000002   PAD
    0x080036c8   0x080036c8   0x0000014e   Code   RO          825    .text.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x08003816   0x08003816   0x00000002   PAD
    0x08003818   0x08003818   0x00000002   Code   RO          829    .text.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x0800381a   0x0800381a   0x00000002   PAD
    0x0800381c   0x0800381c   0x00000220   Code   RO          845    .text.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08003a3c   0x08003a3c   0x0000005a   Code   RO          745    .text.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08003a96   0x08003a96   0x00000002   PAD
    0x08003a98   0x08003a98   0x00000002   Code   RO          747    .text.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08003a9a   0x08003a9a   0x00000002   PAD
    0x08003a9c   0x08003a9c   0x00000002   Code   RO          831    .text.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x08003a9e   0x08003a9e   0x00000002   PAD
    0x08003aa0   0x08003aa0   0x0000010e   Code   RO          753    .text.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x08003bae   0x08003bae   0x00000002   PAD
    0x08003bb0   0x08003bb0   0x00000002   Code   RO         1496    .text.HAL_TIM_PeriodElapsedCallback  btn_app.o
    0x08003bb2   0x08003bb2   0x00000002   PAD
    0x08003bb4   0x08003bb4   0x00000002   Code   RO          835    .text.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x08003bb6   0x08003bb6   0x00000002   PAD
    0x08003bb8   0x08003bb8   0x000001ee   Code   RO         1058    .text.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x08003da6   0x08003da6   0x00000002   PAD
    0x08003da8   0x08003da8   0x0000005c   Code   RO         1514    .text.HAL_UARTEx_RxEventCallback  uart_app.o
    0x08003e04   0x08003e04   0x0000021c   Code   RO         1052    .text.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x08004020   0x08004020   0x00000002   Code   RO         1094    .text.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08004022   0x08004022   0x00000002   PAD
    0x08004024   0x08004024   0x00000594   Code   RO         1088    .text.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x080045b8   0x080045b8   0x00000060   Code   RO         1010    .text.HAL_UART_Init  stm32f4xx_hal_uart.o
    0x08004618   0x08004618   0x0000027e   Code   RO          102    .text.HAL_UART_MspInit  usart.o
    0x08004896   0x08004896   0x00000002   PAD
    0x08004898   0x08004898   0x00000180   Code   RO         1044    .text.HAL_UART_Receive_DMA  stm32f4xx_hal_uart.o
    0x08004a18   0x08004a18   0x00000042   Code   RO         1596    .text.HAL_UART_RxCpltCallback  jy901s_app.o
    0x08004a5a   0x08004a5a   0x00000002   PAD
    0x08004a5c   0x08004a5c   0x00000002   Code   RO         1104    .text.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08004a5e   0x08004a5e   0x00000002   PAD
    0x08004a60   0x08004a60   0x00000192   Code   RO         1026    .text.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08004bf2   0x08004bf2   0x00000002   PAD
    0x08004bf4   0x08004bf4   0x00000002   Code   RO         1098    .text.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08004bf6   0x08004bf6   0x00000002   PAD
    0x08004bf8   0x08004bf8   0x00000002   Code   RO          122    .text.HardFault_Handler  stm32f4xx_it.o
    0x08004bfa   0x08004bfa   0x00000002   PAD
    0x08004bfc   0x08004bfc   0x0000000c   Code   RO          142    .text.I2C1_EV_IRQHandler  stm32f4xx_it.o
    0x08004c08   0x08004c08   0x0000000c   Code   RO          144    .text.I2C2_EV_IRQHandler  stm32f4xx_it.o
    0x08004c14   0x08004c14   0x00000122   Code   RO          241    .text.I2C_DMAAbort  stm32f4xx_hal_i2c.o
    0x08004d36   0x08004d36   0x00000002   PAD
    0x08004d38   0x08004d38   0x0000018c   Code   RO          253    .text.I2C_ITError   stm32f4xx_hal_i2c.o
    0x08004ec4   0x08004ec4   0x000000f8   Code   RO          265    .text.I2C_MasterReceive_BTF  stm32f4xx_hal_i2c.o
    0x08004fbc   0x08004fbc   0x00000146   Code   RO          263    .text.I2C_MasterReceive_RXNE  stm32f4xx_hal_i2c.o
    0x08005102   0x08005102   0x00000002   PAD
    0x08005104   0x08005104   0x00000082   Code   RO          259    .text.I2C_MasterTransmit_BTF  stm32f4xx_hal_i2c.o
    0x08005186   0x08005186   0x00000002   PAD
    0x08005188   0x08005188   0x000000b2   Code   RO          257    .text.I2C_MasterTransmit_TXE  stm32f4xx_hal_i2c.o
    0x0800523a   0x0800523a   0x00000002   PAD
    0x0800523c   0x0800523c   0x000000be   Code   RO          261    .text.I2C_MemoryTransmit_TXE_BTF  stm32f4xx_hal_i2c.o
    0x080052fa   0x080052fa   0x00000002   PAD
    0x080052fc   0x080052fc   0x000000c8   Code   RO          213    .text.I2C_RequestMemoryWrite  stm32f4xx_hal_i2c.o
    0x080053c4   0x080053c4   0x000000ba   Code   RO          181    .text.I2C_WaitOnBTFFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x0800547e   0x0800547e   0x00000002   PAD
    0x08005480   0x08005480   0x000001ca   Code   RO          177    .text.I2C_WaitOnFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x0800564a   0x0800564a   0x00000002   PAD
    0x0800564c   0x0800564c   0x000000d6   Code   RO          295    .text.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08005722   0x08005722   0x00000002   PAD
    0x08005724   0x08005724   0x000000ba   Code   RO          179    .text.I2C_WaitOnTXEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x080057de   0x080057de   0x00000002   PAD
    0x080057e0   0x080057e0   0x00000070   Code   RO         1403    .text.JY901S_Create  jy901s_driver.o
    0x08005850   0x08005850   0x000002f8   Code   RO         1405    .text.JY901S_ProcessBuffer  jy901s_driver.o
    0x08005b48   0x08005b48   0x0000008e   Code   RO         1332    .text.Led_Display   led_driver.o
    0x08005bd6   0x08005bd6   0x00000002   PAD
    0x08005bd8   0x08005bd8   0x00000006   Code   RO         1478    .text.Led_Init      led_app.o
    0x08005bde   0x08005bde   0x00000002   PAD
    0x08005be0   0x08005be0   0x0000000c   Code   RO         1480    .text.Led_Task      led_app.o
    0x08005bec   0x08005bec   0x0000005c   Code   RO           34    .text.MX_DMA_Init   dma.o
    0x08005c48   0x08005c48   0x00000110   Code   RO           25    .text.MX_GPIO_Init  gpio.o
    0x08005d58   0x08005d58   0x00000042   Code   RO           43    .text.MX_I2C1_Init  i2c.o
    0x08005d9a   0x08005d9a   0x00000002   PAD
    0x08005d9c   0x08005d9c   0x00000044   Code   RO           45    .text.MX_I2C2_Init  i2c.o
    0x08005de0   0x08005de0   0x00000196   Code   RO           61    .text.MX_TIM1_Init  tim.o
    0x08005f76   0x08005f76   0x00000002   PAD
    0x08005f78   0x08005f78   0x00000070   Code   RO           65    .text.MX_TIM2_Init  tim.o
    0x08005fe8   0x08005fe8   0x0000006a   Code   RO           67    .text.MX_TIM3_Init  tim.o
    0x08006052   0x08006052   0x00000002   PAD
    0x08006054   0x08006054   0x0000006a   Code   RO           69    .text.MX_TIM4_Init  tim.o
    0x080060be   0x080060be   0x00000002   PAD
    0x080060c0   0x080060c0   0x00000052   Code   RO           71    .text.MX_TIM6_Init  tim.o
    0x08006112   0x08006112   0x00000002   PAD
    0x08006114   0x08006114   0x0000003c   Code   RO           94    .text.MX_UART4_Init  usart.o
    0x08006150   0x08006150   0x0000003c   Code   RO           96    .text.MX_UART5_Init  usart.o
    0x0800618c   0x0800618c   0x0000003c   Code   RO           98    .text.MX_USART1_UART_Init  usart.o
    0x080061c8   0x080061c8   0x0000003c   Code   RO          100    .text.MX_USART2_UART_Init  usart.o
    0x08006204   0x08006204   0x00000002   Code   RO          124    .text.MemManage_Handler  stm32f4xx_it.o
    0x08006206   0x08006206   0x00000002   PAD
    0x08006208   0x08006208   0x000000da   Code   RO         1343    .text.Motor_Create  motor_driver.o
    0x080062e2   0x080062e2   0x00000002   PAD
    0x080062e4   0x080062e4   0x0000006a   Code   RO         1533    .text.Motor_Init    motor_app.o
    0x0800634e   0x0800634e   0x00000002   PAD
    0x08006350   0x08006350   0x00000002   Code   RO          120    .text.NMI_Handler   stm32f4xx_it.o
    0x08006352   0x08006352   0x00000002   PAD
    0x08006354   0x08006354   0x0000021c   Code   RO         1276    .text.OLED_Clear    oled.o
    0x08006570   0x08006570   0x000002c2   Code   RO         1282    .text.OLED_Init     oled.o
    0x08006832   0x08006832   0x00000002   PAD
    0x08006834   0x08006834   0x000003a4   Code   RO         1268    .text.OLED_ShowChar  oled.o
    0x08006bd8   0x08006bd8   0x00000040   Code   RO         1272    .text.OLED_ShowStr  oled.o
    0x08006c18   0x08006c18   0x0000000e   Code   RO         1467    .text.Oled_Init     oled_app.o
    0x08006c26   0x08006c26   0x00000002   PAD
    0x08006c28   0x08006c28   0x00000070   Code   RO         1469    .text.Oled_Task     oled_app.o
    0x08006c98   0x08006c98   0x000000c0   Code   RO         1575    .text.PID_Init      pid_app.o
    0x08006d58   0x08006d58   0x00000002   Code   RO          134    .text.PendSV_Handler  stm32f4xx_it.o
    0x08006d5a   0x08006d5a   0x00000002   PAD
    0x08006d5c   0x08006d5c   0x00000002   Code   RO          130    .text.SVC_Handler   stm32f4xx_it.o
    0x08006d5e   0x08006d5e   0x00000002   PAD
    0x08006d60   0x08006d60   0x00000014   Code   RO         1610    .text.Scheduler_Init  scheduler.o
    0x08006d74   0x08006d74   0x0000004a   Code   RO         1612    .text.Scheduler_Run  scheduler.o
    0x08006dbe   0x08006dbe   0x00000002   PAD
    0x08006dc0   0x08006dc0   0x00000004   Code   RO          136    .text.SysTick_Handler  stm32f4xx_it.o
    0x08006dc4   0x08006dc4   0x000000a8   Code   RO           13    .text.SystemClock_Config  main.o
    0x08006e6c   0x08006e6c   0x00000012   Code   RO         1132    .text.SystemInit    system_stm32f4xx.o
    0x08006e7e   0x08006e7e   0x00000002   PAD
    0x08006e80   0x08006e80   0x0000005c   Code   RO         1624    .text.System_Init   scheduler_task.o
    0x08006edc   0x08006edc   0x0000000c   Code   RO          140    .text.TIM2_IRQHandler  stm32f4xx_it.o
    0x08006ee8   0x08006ee8   0x0000013a   Code   RO          695    .text.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08007022   0x08007022   0x00000002   PAD
    0x08007024   0x08007024   0x0000000c   Code   RO          148    .text.UART5_IRQHandler  stm32f4xx_it.o
    0x08007030   0x08007030   0x0000000a   Code   RO         1092    .text.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x0800703a   0x0800703a   0x00000002   PAD
    0x0800703c   0x0800703c   0x0000017c   Code   RO         1042    .text.UART_DMAError  stm32f4xx_hal_uart.o
    0x080071b8   0x080071b8   0x0000015e   Code   RO         1120    .text.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x08007316   0x08007316   0x00000002   PAD
    0x08007318   0x08007318   0x00000018   Code   RO         1122    .text.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08007330   0x08007330   0x000000fe   Code   RO         1090    .text.UART_Receive_IT  stm32f4xx_hal_uart.o
    0x0800742e   0x0800742e   0x00000002   PAD
    0x08007430   0x08007430   0x000000e6   Code   RO         1014    .text.UART_SetConfig  stm32f4xx_hal_uart.o
    0x08007516   0x08007516   0x00000002   PAD
    0x08007518   0x08007518   0x0000000c   Code   RO          146    .text.USART1_IRQHandler  stm32f4xx_it.o
    0x08007524   0x08007524   0x00000002   Code   RO          128    .text.UsageFault_Handler  stm32f4xx_it.o
    0x08007526   0x08007526   0x00000002   PAD
    0x08007528   0x08007528   0x0000002e   Code   RO         1494    .text.app_btn_init  btn_app.o
    0x08007556   0x08007556   0x00000002   PAD
    0x08007558   0x08007558   0x0000000e   Code   RO         1498    .text.btn_task      btn_app.o
    0x08007566   0x08007566   0x00000002   PAD
    0x08007568   0x08007568   0x0000004c   Code   RO         1205    .text.ebtn_init     ebtn.o
    0x080075b4   0x080075b4   0x0000009a   Code   RO         1209    .text.ebtn_process  ebtn.o
    0x0800764e   0x0800764e   0x00000002   PAD
    0x08007650   0x08007650   0x000002ac   Code   RO         1207    .text.ebtn_process_with_curr_state  ebtn.o
    0x080078fc   0x080078fc   0x00000034   Code   RO         1598    .text.jy901s_init   jy901s_app.o
    0x08007930   0x08007930   0x00000046   Code   RO           11    .text.main          main.o
    0x08007976   0x08007976   0x00000002   PAD
    0x08007978   0x08007978   0x0000003e   Code   RO         1510    .text.my_printf     uart_app.o
    0x080079b6   0x080079b6   0x00000002   PAD
    0x080079b8   0x080079b8   0x0000003a   Code   RO         1323    .text.oled_printf   oled_driver.o
    0x080079f2   0x080079f2   0x00000002   PAD
    0x080079f4   0x080079f4   0x0000001a   Code   RO         1297    .text.pid_init      pid.o
    0x08007a0e   0x08007a0e   0x00000002   PAD
    0x08007a10   0x08007a10   0x00000006   Code   RO         1299    .text.pid_set_target  pid.o
    0x08007a16   0x08007a16   0x00000002   PAD
    0x08007a18   0x08007a18   0x00000024   Code   RO         1492    .text.prv_btn_event  btn_app.o
    0x08007a3c   0x08007a3c   0x00000034   Code   RO         1490    .text.prv_btn_get_state  btn_app.o
    0x08007a70   0x08007a70   0x0000019a   Code   RO         1243    .text.prv_process_btn  ebtn.o
    0x08007c0a   0x08007c0a   0x00000002   PAD
    0x08007c0c   0x08007c0c   0x00000044   Code   RO         1153    .text.rt_ringbuffer_data_len  ringbuffer.o
    0x08007c50   0x08007c50   0x000000b8   Code   RO         1157    .text.rt_ringbuffer_get  ringbuffer.o
    0x08007d08   0x08007d08   0x0000000e   Code   RO         1149    .text.rt_ringbuffer_init  ringbuffer.o
    0x08007d16   0x08007d16   0x00000002   PAD
    0x08007d18   0x08007d18   0x000000b2   Code   RO         1151    .text.rt_ringbuffer_put  ringbuffer.o
    0x08007dca   0x08007dca   0x00000002   PAD
    0x08007dcc   0x08007dcc   0x00000040   Code   RO         1512    .text.uart_init     uart_app.o
    0x08007e0c   0x08007e0c   0x00000054   Code   RO         1516    .text.uart_task     uart_app.o
    0x08007e60   0x08007e60   0x0000003e   Code   RO         1840    CL$$btod_d2e        c_w.l(btod.o)
    0x08007e9e   0x08007e9e   0x00000046   Code   RO         1842    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08007ee4   0x08007ee4   0x00000060   Code   RO         1841    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08007f44   0x08007f44   0x00000338   Code   RO         1850    CL$$btod_div_common  c_w.l(btod.o)
    0x0800827c   0x0800827c   0x000000dc   Code   RO         1847    CL$$btod_e2e        c_w.l(btod.o)
    0x08008358   0x08008358   0x0000002a   Code   RO         1844    CL$$btod_ediv       c_w.l(btod.o)
    0x08008382   0x08008382   0x0000002a   Code   RO         1843    CL$$btod_emul       c_w.l(btod.o)
    0x080083ac   0x080083ac   0x00000244   Code   RO         1849    CL$$btod_mult_common  c_w.l(btod.o)
    0x080085f0   0x080085f0   0x00000030   Code   RO         1881    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08008620   0x08008620   0x0000000e   Code   RO         1735    i._is_digit         c_w.l(__printf_wp.o)
    0x0800862e   0x0800862e   0x00000002   PAD
    0x08008630   0x08008630   0x0000002c   Code   RO         1865    locale$$code        c_w.l(lc_numeric_c.o)
    0x0800865c   0x0800865c   0x0000002c   Code   RO         1893    locale$$code        c_w.l(lc_ctype_c.o)
    0x08008688   0x08008688   0x00000062   Code   RO         1660    x$fpl$d2f           fz_wm.l(d2f.o)
    0x080086ea   0x080086ea   0x00000002   PAD
    0x080086ec   0x080086ec   0x000002b8   Code   RO         1663    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x080089a4   0x080089a4   0x0000009c   Code   RO         1770    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08008a40   0x08008a40   0x0000000c   Code   RO         1772    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08008a4c   0x08008a4c   0x00000056   Code   RO         1666    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08008aa2   0x08008aa2   0x0000008c   Code   RO         1774    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x08008b2e   0x08008b2e   0x0000001a   Code   RO         1967    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08008b48   0x08008b48   0x0000000a   Code   RO         1776    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x08008b52   0x08008b52   0x00000004   Code   RO         1780    x$fpl$printf1       fz_wm.l(printf1.o)
    0x08008b56   0x08008b56   0x00000004   Code   RO         1782    x$fpl$printf2       fz_wm.l(printf2.o)
    0x08008b5a   0x08008b5a   0x00000000   Code   RO         1788    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08008b5a   0x08008b5a   0x00000002   PAD
    0x08008b5c   0x08008b5c   0x00000008   Data   RO         1694    .constdata          c_w.l(_printf_wctomb.o)
    0x08008b64   0x08008b64   0x00000028   Data   RO         1723    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x08008b8c   0x08008b8c   0x00000011   Data   RO         1743    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08008b9d   0x08008b9d   0x00000026   Data   RO         1815    .constdata          c_w.l(_printf_fp_hex.o)
    0x08008bc3   0x08008bc3   0x00000001   PAD
    0x08008bc4   0x08008bc4   0x00000094   Data   RO         1838    .constdata          c_w.l(bigflt0.o)
    0x08008c58   0x08008c58   0x00000010   Data   RO         1137    .rodata.AHBPrescTable  system_stm32f4xx.o
    0x08008c68   0x08008c68   0x00000008   Data   RO         1138    .rodata.APBPrescTable  system_stm32f4xx.o
    0x08008c70   0x08008c70   0x00000228   Data   RO         1284    .rodata.F6X8        oled.o
    0x08008e98   0x08008e98   0x000005f0   Data   RO         1288    .rodata.F8X16       oled.o
    0x08009488   0x08009488   0x00000008   Data   RO          478    .rodata.cst8        stm32f4xx_hal_dma.o
    0x08009490   0x08009490   0x0000000e   Data   RO         1501    .rodata.defaul_ebtn_param  btn_app.o
    0x0800949e   0x0800949e   0x00000002   PAD
    0x080094a0   0x080094a0   0x00000020   Data   RO         2028    Region$$Table       anon$$obj.o
    0x080094c0   0x080094c0   0x0000001c   Data   RO         1864    locale$$data        c_w.l(lc_numeric_c.o)
    0x080094dc   0x080094dc   0x00000110   Data   RO         1892    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080095f0, Size: 0x00000ee0, Max: 0x0001c000, ABSOLUTE, COMPRESSED[0x00000080])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000008   Data   RW          656    .data..L_MergedGlobals  stm32f4xx_hal.o
    0x20000008   COMPRESSED   0x00000054   Data   RW         1583    .data..L_MergedGlobals  pid_app.o
    0x2000005c   COMPRESSED   0x00000001   Data   RW         1334    .data.Led_Display.temp_old  led_driver.o
    0x2000005d   COMPRESSED   0x00000003   PAD
    0x20000060   COMPRESSED   0x00000004   Data   RW         1136    .data.SystemCoreClock  system_stm32f4xx.o
    0x20000064   COMPRESSED   0x000000a8   Data   RW         1500    .data.btns          btn_app.o
    0x2000010c   COMPRESSED   0x00000016   Data   RW         1287    .data.initcmd1      oled.o
    0x20000122   COMPRESSED   0x00000002   PAD
    0x20000124   COMPRESSED   0x0000003c   Data   RW         1615    .data.scheduler_task  scheduler.o
    0x20000160   COMPRESSED   0x00000004   Data   RW         1482    .data.ucLed         led_app.o
    0x20000164   COMPRESSED   0x00000004   PAD
    0x20000168        -       0x00000060   Zero   RW         1884    .bss                c_w.l(libspace.o)
    0x200001c8        -       0x0000004c   Zero   RW         1524    .bss..L_MergedGlobals  uart_app.o
    0x20000214        -       0x00000060   Zero   RW         1541    .bss..L_MergedGlobals  motor_app.o
    0x20000274        -       0x00000020   Zero   RW         1553    .bss..L_MergedGlobals  encoder_app.o
    0x20000294        -       0x00000008   Zero   RW         1566    .bss..L_MergedGlobals  gray_app.o
    0x2000029c        -       0x00000028   Zero   RW         1601    .bss..L_MergedGlobals  jy901s_app.o
    0x200002c4        -       0x0000007c   Zero   RW         1584    .bss..L_MergedGlobals.1  pid_app.o
    0x20000340        -       0x00000078   Zero   RW         1585    .bss..L_MergedGlobals.2  pid_app.o
    0x200003b8        -       0x00000034   Zero   RW         1245    .bss.ebtn_default   ebtn.o
    0x200003ec        -       0x00000060   Zero   RW          110    .bss.hdma_uart5_rx  usart.o
    0x2000044c        -       0x00000060   Zero   RW          111    .bss.hdma_usart1_rx  usart.o
    0x200004ac        -       0x00000054   Zero   RW           51    .bss.hi2c1          i2c.o
    0x20000500        -       0x00000054   Zero   RW           52    .bss.hi2c2          i2c.o
    0x20000554        -       0x00000048   Zero   RW           81    .bss.htim1          tim.o
    0x2000059c        -       0x00000048   Zero   RW           82    .bss.htim2          tim.o
    0x200005e4        -       0x00000048   Zero   RW           83    .bss.htim3          tim.o
    0x2000062c        -       0x00000048   Zero   RW           84    .bss.htim4          tim.o
    0x20000674        -       0x00000048   Zero   RW           85    .bss.htim6          tim.o
    0x200006bc        -       0x00000048   Zero   RW          108    .bss.huart1         usart.o
    0x20000704        -       0x00000048   Zero   RW          109    .bss.huart2         usart.o
    0x2000074c        -       0x00000048   Zero   RW          106    .bss.huart4         usart.o
    0x20000794        -       0x00000048   Zero   RW          107    .bss.huart5         usart.o
    0x200007dc        -       0x0000007c   Zero   RW         1600    .bss.jy901s         jy901s_app.o
    0x20000858        -       0x00000001   Zero   RW         1614    .bss.task_num       scheduler.o
    0x20000859   COMPRESSED   0x00000003   PAD
    0x2000085c        -       0x00000040   Zero   RW         1518    .bss.uart_out_buffer  uart_app.o
    0x2000089c        -       0x00000040   Zero   RW         1521    .bss.uart_ringbuffer_pool  uart_app.o
    0x200008dc        -       0x00000004   Zero   RW          655    .bss.uwTick         stm32f4xx_hal.o
    0x200008e0        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20000ae0        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x08009670, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       150          8         14        168          0       5646   btn_app.o
        92          0          0          0          0       3486   dma.o
      1324          0          0          0         52      20922   ebtn.o
       136         12          0          0         32       4429   encoder_app.o
       136         16          0          0          0       4287   encoder_driver.o
       272          0          0          0          0       2250   gpio.o
         2          0          0          0          8       1309   gray_app.o
       362          0          0          0        168       7364   i2c.o
       118          0          0          0        164       5035   jy901s_app.o
       872         28          0          0          0      24897   jy901s_driver.o
        18          0          0          4          0        776   led_app.o
       142          0          0          1          0       1512   led_driver.o
       244          0          0          0          0       2892   main.o
       106          0          0          0         96       5163   motor_app.o
       218          0          0          0          0      12520   motor_driver.o
      2242          0       2072         22          0      17714   oled.o
       126         32          0          0          0        754   oled_app.o
        58          0          0          0          0       1080   oled_driver.o
        32          0          0          0          0       2903   pid.o
       192          4          0         84        244       6567   pid_app.o
       444          0          0          0          0       6476   ringbuffer.o
        94          0          0         60          1       1196   scheduler.o
        92         24          0          0          0       4413   scheduler_task.o
        64         26        392          0       1536        832   startup_stm32f407xx.o
       204          0          0          8          4       7284   stm32f4xx_hal.o
       196          0          0          0          0      10701   stm32f4xx_hal_cortex.o
      1152          6          8          0          0      10513   stm32f4xx_hal_dma.o
       434          0          0          0          0       5331   stm32f4xx_hal_gpio.o
      5258         16          0          0          0      44385   stm32f4xx_hal_i2c.o
        56          0          0          0          0       1439   stm32f4xx_hal_msp.o
      1480          0          0          0          0       7455   stm32f4xx_hal_rcc.o
      2604          6          0          0          0      57409   stm32f4xx_hal_tim.o
       266          0          0          0          0      21466   stm32f4xx_hal_tim_ex.o
      4598          0          0          0          0      32224   stm32f4xx_hal_uart.o
       104          0          0          0          0       7560   stm32f4xx_it.o
        18          0         24          4          0       2568   system_stm32f4xx.o
      1158          0          0          0        360      12211   tim.o
       302         12          0          0        204       4993   uart_app.o
       878          0          0          0        480       8167   usart.o

    ----------------------------------------------------------------------
     26436        <USER>       <GROUP>        356       3356     378129   Object Totals
         0          0         32          0          0          0   (incl. Generated)
       192          0          2          5          7          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        94          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        72          0          0          0          0         96   _c16rtomb.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1052          0          0          0          0        148   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112          8          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        16          0          0          0          0         68   _snputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        668   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       240          0          0          0          0        100   lludivv7m.o
        88          0          0          0          0         76   memcmp.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       124          0          0          0          0         88   strcmpv7em.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        60          4          0          0          0         80   vsnprintf.o
        98          4          0          0          0        140   d2f.o
       696        140          0          0          0        264   ddiv.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        26          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
      8848        <USER>        <GROUP>          0         96       5832   Library Totals
        24          4          1          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7544        264        551          0         96       4320   c_w.l
      1232        156          0          0          0       1388   fz_wm.l
        48          0          0          0          0        124   m_wm.l

    ----------------------------------------------------------------------
      8848        <USER>        <GROUP>          0         96       5832   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     35284        614       3096        356       3452     380049   Grand Totals
     35284        614       3096        128       3452     380049   ELF Image Totals (compressed)
     35284        614       3096        128          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                38380 (  37.48kB)
    Total RW  Size (RW Data + ZI Data)              3808 (   3.72kB)
    Total ROM Size (Code + RO Data + RW Data)      38508 (  37.61kB)

==============================================================================

