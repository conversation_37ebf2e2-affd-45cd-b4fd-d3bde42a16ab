# 正方形循迹功能实现说明

## 项目要求
根据图片要求，实现智能车在100cm×100cm正方形轨道上的自动循迹功能：
- 轨道：黑色线宽1.8cm±0.2cm
- 路径：A→B→C→D→A逆时针方向行驶
- 场地：100cm×100cm正方形

## 主要修改内容

### 1. 启用循迹任务 (User/Scheduler.c)
```c
// 启用灰度传感器和PID控制任务
{Gray_Task, 10, 0},        // 启用灰度传感器任务
{PID_Task,5,0}             // 启用PID控制任务
```

### 2. PID参数优化 (User/App/pid_app.c)
- **基础速度调整**：从80降低到60，适应100cm×100cm场地
- **循迹PID参数优化**：
  - Kp: 10.0 → 8.0 (降低P参数，适应1.8cm线宽)
  - Ki: 0.0 → 0.0001 (增加微小积分项)
  - Kd: 160.0 → 120.0 (适当降低D参数)
- **默认启用循迹模式**：
  - pid_running = true (启动时自动开启)
  - pid_control_mode = 1 (默认循迹模式)

### 3. 正方形轨道逻辑 (User/Scheduler_Task.c)
- **系统模式设置**：system_mode = 2 (正方形一圈模式)
- **启用Car_State_Update函数**：实现A→B→C→D→A循环逻辑
- **路径点检测**：
  - 基于灰度传感器全黑检测 (Digtal == 0xFF)
  - 基于距离检测备用方案 (distance > 9500)
- **状态转换逻辑**：
  - 点1(B): 循迹模式
  - 点2(C): 角度控制模式，转弯-90度
  - 点3(D): 循迹模式  
  - 点4(A): 重置计数，继续循环

### 4. 灰度传感器优化 (User/App/gray_app.c)
```c
// 权重优化，适应1.8cm线宽
float gray_weights[8] = {-3.5f, -2.5f, -1.5f, -0.5f, 0.5f, 1.5f, 2.5f, 3.5f};
```

### 5. 按键控制增强 (User/App/btn_app.c)
- **按键3**：启动/停止PID控制
- **按键4**：重置系统状态（point_count和distance清零）

### 6. OLED显示优化 (User/App/oled_app.c)
显示内容包括：
- 系统模式和PID状态
- 当前路径点计数
- 行驶距离
- 循迹误差值
- 8路灰度传感器状态

### 7. 定时器中断处理 (User/Scheduler_Task.c)
- **启用HAL_TIM_PeriodElapsedCallback**：1ms定时器中断
- **距离累计**：基于左轮编码器速度
- **路径点检测**：双重检测机制确保可靠性

## 控制流程

### 启动流程
1. 系统初始化 → 自动启用PID控制
2. 默认循迹模式 → 开始沿黑线行驶
3. OLED显示当前状态

### 运行流程
1. **直线段**：循迹模式，速度55
2. **接近转弯点**：距离>8000时减速到45
3. **检测路径点**：全黑线或距离触发
4. **转弯控制**：切换到角度模式，-90度转弯
5. **继续循迹**：转弯完成后恢复循迹模式

### 按键控制
- **按键1/2**：LED控制
- **按键3**：启动/停止PID (LED2指示状态)
- **按键4**：系统重置 (LED3闪烁)

## 技术特点

1. **多环PID控制**：速度环+循迹环+角度环
2. **状态机管理**：不同路段采用不同控制策略
3. **双重检测**：灰度+距离双重路径点检测
4. **参数优化**：针对1.8cm线宽和100cm场地调优
5. **实时显示**：OLED显示系统状态和传感器数据
6. **手动控制**：按键启停和重置功能

## 预期效果

小车将能够：
- 稳定沿1.8cm黑线循迹
- 在100cm×100cm正方形轨道上逆时针行驶
- 自动检测A、B、C、D四个路径点
- 在转弯点自动切换控制模式
- 完成A→B→C→D→A的循环轨迹
- 通过OLED实时监控运行状态
